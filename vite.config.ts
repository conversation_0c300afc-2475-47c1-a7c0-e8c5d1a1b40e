import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

import { createProxyMiddleware } from 'http-proxy-middleware'
//@ts-ignore
import dotenv from 'dotenv'

dotenv.config()

const isDev = process.env['NODE_ENV'] == 'development'
//console.log('isDev', isDev)

const rproxy = () => ({
  name: 'configure-server',
  configureServer(server) {
    server.middlewares.use(
      createProxyMiddleware('/api', {
        target: process.env[isDev ? 'VITE_API_URL_DEV' : 'VITE_API_URL'],
        changeOrigin: true,
        pathRewrite: { '^/api': '' },
        toProxy: true,
        xfwd: true
      })
    )

    server.middlewares.use(
      createProxyMiddleware('/trpc', {
        target: process.env[isDev ? 'VITE_API_URL_DEV' : 'VITE_API_URL'],
        changeOrigin: true,
        // pathRewrite: { '^/api': '' },
        toProxy: true,
        xfwd: true
      })
    )

    server.middlewares.use(
      '/socket.io',
      createProxyMiddleware('/socket.io', {
        target: process.env[isDev ? 'VITE_API_URL_DEV' : 'VITE_API_URL'],
        changeOrigin: true,
        ws: true
      })
    )
  }
})

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(), rproxy()],
  server: {
    port: 5174
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
})
