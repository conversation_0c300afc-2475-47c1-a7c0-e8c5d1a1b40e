<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import { useUserStore } from './stores/user'
import Login from './components/Login.vue'
import Navbar from './components/ui/Navbar.vue'
// import ConfirmPopup from 'primevue/confirmpopup';
// import ConfirmDialog from 'primevue/confirmdialog';
import Toast from 'primevue/toast'
import ConfirmDialog from 'primevue/confirmdialog'
import { nextTick, onMounted } from 'vue'
import { api } from './lib/_api'

import type { ApiInitData } from '@/lib/interfaces/ApiSettings'
import { useSettingsStore } from './stores/apisettings'
import { useAppStore } from './stores/app'

const { userData, setUserData } = useUserStore()
const { setSettingsData } = useSettingsStore()
const { setAppStoreData } = useAppStore()
// const {} =

async function appInit() {
  const apiInitDataRes = await api('/cpan/init')
  if (apiInitDataRes.ok) {
    const apiInitData: ApiInitData = apiInitDataRes.body
    setSettingsData(apiInitData.settings)
    setAppStoreData({ categories: apiInitData.categories })
  } else {
    // throw error
  }
}

onMounted(async () => {
  await appInit()
})
nextTick(async () => await appInit())
</script>

<template>
  <div id="print-container"></div>
  <div class="no-print" v-if="userData?.user_id">
    <header class="sticky top-0 z-10">
      <Navbar />
    </header>

    <main class="p-3 mb-6">
      <RouterView />
    </main>
  </div>
  <div class="no-print" v-else>
    <div class="flex items-center justify-center h-screen p-5">
      <div class="">
        <Login />
      </div>
    </div>
  </div>

  <!-- <ConfirmPopup/> -->
  <ConfirmDialog></ConfirmDialog>
  <Toast />
</template>

<style>
.p-scrollpanel.custombar1 .p-scrollpanel-wrapper {
  border-right: 7px solid var(--surface-ground);
}

.p-scrollpanel.custombar1 .p-scrollpanel-bar {
  background-color: var(--primary-300);
  opacity: 1;
  transition: background-color 0.3s;
}

.p-scrollpanel.custombar1 .p-scrollpanel-bar:hover {
  background-color: var(--primary-400);
}
</style>
