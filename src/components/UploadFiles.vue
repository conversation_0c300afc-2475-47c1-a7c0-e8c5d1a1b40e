<template>
  <div class="card">
    <Toast />
    <FileUpload name="files[]" :url="getUploadImgURL()" @upload="onTemplatedUpload($event)" :multiple="true" @select="onSelectedFiles">
      <template #header="{ chooseCallback, uploadCallback, clearCallback, files }">
        <div class="flex flex-wrap justify-content-between align-items-center flex-1 gap-2">
          <div class="flex gap-2">
            <Button text @click="chooseCallback()" label="Выбрать"></Button>
            <Button text @click="uploadEvent(uploadCallback)" label="Отправить" severity="success" :disabled="!files || files.length === 0"></Button>
            <Button text @click="clearCallback()" label="Отмена" severity="danger" :disabled="!files || files.length === 0"></Button>
          </div>
          <ProgressBar :value="totalSizePercent" :showValue="false" :class="['md:w-20rem h-1rem w-full md:ml-auto', { 'exceeded-progress-bar': totalSizePercent > 100 }]"></ProgressBar>
        </div>
      </template>
      <template #content="{ files, uploadedFiles, removeUploadedFileCallback, removeFileCallback }">
        <div v-if="files.length > 0">
          <h5>Ожидают загрузки</h5>
          <div class="flex flex-wrap p-0 gap-5 mt-3">
            <div v-for="(file, index) of files" :key="file.name + file.type + file.size" class="card m-0 flex flex-column border-1 items-center gap-3">
              <div>
                <Image role="presentation" :alt="file.name" :src="file.objectURL" width="100" height="50" preview class="shadow-lg rounded-lg" />
              </div>
              <span class="font-semibold text-ellipsis">{{ String(file.name).slice(0, 20) }}<span v-show="String(file.name).length > 20">...</span></span>
              <div>{{ formatSize(file.size) }}</div>
              <!-- <i class="pi pi-pause" /> -->
              <!-- <Badge value="Pending" severity="warning" /> -->
              <Button icon="pi pi-times" @click="onRemoveTemplatingFile(file, removeFileCallback, index)" text severity="danger" />
            </div>
          </div>
        </div>

        <div v-if="uploadedFiles.length > 0">
          <h5>Загружены</h5>
          <div class="flex flex-wrap gap-5 mt-3">
            <div v-for="(file, index) of uploadedFiles" :key="file.name + file.type + file.size" class="card m-0 flex flex-column border-1 items-center gap-3">
              <div>
                <Image role="presentation" :alt="file.name" :src="file.objectURL" width="100" height="50" preview class="shadow-lg rounded-lg" />
              </div>
              <span class="font-semibold text-ellipsis">{{ String(file.name).slice(0, 20) }}<span v-show="String(file.name).length > 20">...</span></span>

              <div>{{ formatSize(file.size) }}</div>
              <!-- <Badge value="Completed" class="mt-3" severity="success" /> -->
              <i class="pi pi-check text-green-500" />
              <!-- <Button icon="pi pi-times" @click="removeUploadedFileCallback(index)" outlined rounded severity="danger" /> -->
            </div>
          </div>
        </div>
      </template>
      <template #empty>
        <div class="flex align-items-center justify-content-center flex-column">
          <i class="pi pi-cloud-upload border-2 rounded-lg p-5 text-8xl text-400 border-400" />
          <p class="ml-3 mt-4 mb-0">Перетащите файлы</p>
        </div>
      </template>
    </FileUpload>
  </div>
</template>

<script setup lang="ts">
// import Badge from 'primevue/badge'
import Button from 'primevue/button'
import FileUpload from 'primevue/fileupload'
import Image from 'primevue/image'
import ProgressBar from 'primevue/progressbar'
import Toast from 'primevue/toast'
import { useToast } from 'primevue/usetoast'
import { ref, defineEmits } from 'vue'

const emit = defineEmits(['upload'])
const toast = useToast()

const totalSize = ref(0)
const totalSizePercent = ref(0)
const files = ref([])

const props = defineProps<{
  applyToProduct: boolean
}>()

const onRemoveTemplatingFile = (file, removeFileCallback, index) => {
  removeFileCallback(index)
  totalSize.value -= parseInt(formatSize(file.size))
  totalSizePercent.value = totalSize.value / 10
}

const onClearTemplatingUpload = (clear) => {
  clear()
  totalSize.value = 0
  totalSizePercent.value = 0
}

const onSelectedFiles = (event) => {
  files.value = event.files
  files.value.forEach((file) => {
    totalSize.value += parseInt(formatSize(file.size))
  })
}

const uploadEvent = (callback) => {
  totalSizePercent.value = totalSize.value / 10
  callback()
}

const onTemplatedUpload = () => {
  toast.add({ severity: 'info', summary: 'Success', detail: 'Файлы загружены', life: 3000 })
  emit('upload')
}

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function getUploadImgURL() {
  const base = import.meta.env.DEV ? import.meta.env['VITE_API_URL_DEV'] : import.meta.env['VITE_API_URL']

  const qs = new URLSearchParams()

  props.applyToProduct && qs.set('applyToProduct', '1')

  return base + `/cpan/files/upload?${qs.toString()}`
}
</script>
