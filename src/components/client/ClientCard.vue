<script setup lang="ts">
import { defineProps, onBeforeMount, onMounted, ref, watch } from 'vue'

import Inplace from 'primevue/inplace'
import Chip from 'primevue/chip'
import Accordion from 'primevue/accordion'
import AccordionTab from 'primevue/accordiontab'
import Listbox from 'primevue/listbox'
import TotalSalesChart from '@/components/charts/TotalSalesChart.vue'
import OrdersChart from '@/components/charts/OrdersChart.vue'
import { useToast } from 'primevue/usetoast'
import { useConfirm } from 'primevue/useconfirm'
import InputNumber from 'primevue/inputnumber'

import { trnColumns } from '@/lib/trnColumns'
import { api } from '@/lib/_api'
import ClientData from '@/components/client/ClientData.vue'
import Reconact from '@/components/client/Reconact.vue'

import AutoComplete from 'primevue/autocomplete'

import SelectButton from 'primevue/selectbutton'

import Menu from 'primevue/menu'
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import <PERSON>rollPanel from 'primevue/scrollpanel'
import Divider from 'primevue/divider'

import { getColorByStatus } from '@/lib/getColorClassByStatus'

const toast = useToast()
const confirm = useConfirm()

const props = defineProps({
  data: {}
})

const countersLoading = ref(true),
  ordersCounters = ref(),
  salesCounters = ref(),
  prev_ordersCounters = ref(),
  prev_salesCounters = ref(),
  offerParams = ref({ countryId: 643 }),
  menu = ref(),
  resetPasswordValue = ref(''),
  makeOfferDialogShow = ref(false),
  documents = ref([]),
  filteredProds = ref(),
  offerProducts = ref([]),
  RKfilterModel = ref(),
  offerLoading = ref(false),
  restorePasswordDialog = ref(false),
  org = ref({})

const reconactDialogShow = ref(false)

const menuItems = ref([
  {
    label: 'Документы',
    items: [
      {
        label: 'Коммерческое предложение',
        icon: 'pi pi-file-excel',
        command: () => {
          makeOfferDialogShow.value = true
        }
      },
      {
        label: 'Акт сверки',
        icon: 'pi pi-file-excel',
        command: () => {
          reconactDialogShow.value = true
        }
      }
    ]
  },
  {
    label: 'Сбросить пароль',
    icon: 'pi pi-undo',
    command: () => {
      restorePasswordDialog.value = true
    }
  }
])

const clientFields = [
  'client_country',
  'client_city',
  'client_street',
  'client_house',
  'client_flat',
  'client_postindex',
  // 'client_id',
  'client_mail',
  'client_name',
  // 'client_number',loadCounters
  'client_phone',
  'client_cdekid'
]

const orgFields = ['org_name', 'org_adress', 'org_inn', 'org_kpp', 'org_bank', 'org_bik', 'org_kschet', 'org_rschet', 'org_vat']

const orgIcons = {
  'org_name': 'pi-briefcase',
  'org_adress': 'pi-map-marker',
  'org_inn': 'pi-file',
  'org_kpp': 'pi-file',
  'org_bank': 'pi-building',
  'org_bik': 'pi-building',
  'org_rschet': 'pi-credit-card',
  'org_kschet': 'pi-credit-card',
  'org_vat': 'pi-info'
}

const fieldIcons = {
  client_city: 'pi-map-marker',
  client_phone: 'pi-phone',
  'client_country': 'pi-flag',
  'client_flat': 'pi-info',
  'client_house': 'pi-info',
  // 'client_id': '',
  'client_mail': 'pi-inbox',
  'client_name': 'pi-user',
  'client_number': 'pi-user',
  'client_postindex': 'pi-map',
  'client_street': 'pi-map-marker'
}

const loadCounters = async () => {
  const res = await api('/cpan/statistics/byclient/?id=' + props.data.client_id)
  const { ordersCount, totalSalesCount, prev_ordersCount, prev_totalSalesCount } = res.body

  ordersCounters.value = ordersCount
  salesCounters.value = totalSalesCount

  prev_ordersCounters.value = prev_ordersCount
  prev_salesCounters.value = prev_totalSalesCount

  countersLoading.value = false
}

const del = (event) => {
  confirm.require({
    target: event.currentTarget,
    message: 'Удалить клиента: ' + props.data.client_name,
    icon: 'pi pi-info-circle',
    acceptClass: 'p-button-danger',
    acceptIcon: 'pi pi-check',
    rejectIcon: 'pi pi-times',
    acceptLabel: 'Подтвердить',
    rejectLabel: 'Отменить',
    accept: () => {
      initDel()
    },
    reject: () => {}
  })
}

const initDel = async () => {
  try {
    const res = await api('/cpan/client/delete/' + props.data.client_id)
    if (res.ok) {
      toast.add({ severity: 'success', summary: 'Клиент удален', detail: '', life: 3000 })
    } else {
      toast.add({ severity: 'error', summary: 'Ошибка', detail: res.message, life: 6000 })
    }
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Ошибка cервера', detail: error, life: 6000 })
  }
}

const saveUpdate = async () => {
  let payload = {}

  Object.keys(props.data).map((key) => {
    if (key.startsWith('client_')) {
      payload[key] = props.data[key]
    }
  })

  if (props.data.org) {
    payload.org = props.data.org

    delete payload.org.created_at
    delete payload.org.updated_at
  }

  try {
    const res = await api('/cpan/client/update', {
      method: 'POST',
      data: payload
    })

    if (res.ok) {
      toast.add({ severity: 'success', summary: 'Данные клиента обновлены', detail: '', life: 3000 })
    } else {
      toast.add({ severity: 'error', summary: 'Ошибка', detail: res.message, life: 6000 })
    }
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Ошибка cервера', detail: error, life: 6000 })
  }
}

const toggleMenu = (event) => {
  menu.value.toggle(event)
}

const makeOffer = async () => {
  const clFl = ['client_postindex', 'client_country', 'client_adress', 'client_city', 'client_street', 'client_house', 'client_flat', 'client_name', 'client_mail', 'client_phone']
  const orgFL = ['org_adress', 'org_name', 'org_inn', 'org_kpp', 'org_rschet', 'org_kschet', 'org_bik', 'org_bank', 'org_vat']

  offerLoading.value = true

  let payload = {
    ...offerParams.value,
    productData: offerProducts.value,
    shippingType: offerParams.value.shippingType?.value || 'express',
    client: {
      fullorgname: props.data.org
        ? `${orgFL
            .map((key) => props.data.org[key])
            .filter((i) => i)
            .join(', ')}`
        : `${clFl.map((key) => props.data[key]).join(', ')}`
    }
  }

  Object.keys(props.data).map((key) => {
    if (key.startsWith('client_')) {
      payload.client[key] = props.data[key]
    }
  })

  if (props.data.org) {
    payload.org = props.data.org
  }

  // //console.log('payload: ', payload);
  // return payload

  const res = await api('/service/documents/offer/', {
    method: 'POST',
    data: payload,
    blob: true
  })

  offerLoading.value = false

  // //console.log('res:', res)

  const link = document.createElement('a')

  link.href = window.URL.createObjectURL(res.body)
  link.download = `${payload.filename.split('.')[0]}_${new Date().getTime()}.xlsx`

  link.click()
}

const loadDocuments = async () => {
  const res = await api('/service/documents/list/')

  if (res.ok) {
    try {
      documents.value = res.body
        .filter((i) => String(i.name).includes('.'))
        // .map(i => i.name.split('.')[0])
        .map((i) => i.name)
        .filter((i) => i)
    } catch (error) {}
  }
}

function RKinputChangeHandler({ value }, id) {
  if (value < 1) {
    offerProducts.value = offerProducts.value.filter((i) => i.id != id)
  }
}

async function findRKitemHandler({ query: value }) {
  if (String(value).length > 3) {
    // const { body } = await api('/cpan/products/?' + 'searchvalue=' + value)
    const { body } = await api('/search/fast/' + value)
    filteredProds.value = body.products //.data
  } else {
    filteredProds.value = []
  }
}

function addToRK({ value }) {
  offerProducts.value.unshift({
    id: value.prod_id,
    qty: 1,
    product: value
  })
  RKfilterModel.value = undefined
}

async function resetPassword() {
  const res = await api(`/cpan/client/restpassword/${props.data.client_id}?value=` + resetPasswordValue.value)
  if (res.ok) {
    toast.add({ severity: 'success', summary: 'Пароль отправлен на электронную почту', detail: '', life: 3000 })
  } else {
    toast.add({ severity: 'error', summary: 'Ошибка сервера!', detail: '', life: 3000 })
  }
}

function reconactPrint() {
  //console.log('@reconact print');
  reconactDialogShow.value = false
}

onMounted(() => {
  loadCounters()
  loadDocuments()

  try {
    props.data.orders.map((order) => {
      order.order_coupons ? (order.order_coupons = JSON.parse(order.order_coupons)) : ''
    })
  } catch (error) {
    //console.log(error)
  }
})
</script>

<template>
  <div>
    <div class="lg:flex lg:items-center lg:justify-between">
      <div class="min-w-0 flex-1">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-zinc-100 sm:truncate sm:text-xl sm:tracking-tight">
          {{ data.client_name }} <Chip class="ml-3 py-1"> {{ data.client_number }} </Chip>
          <Chip class="ml-3 py-1">Нейтральный</Chip>
        </h2>

        <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-200">
            <!-- Heroicon name: mini/briefcase -->
            <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path
                fill-rule="evenodd"
                d="M6 3.75A2.75 2.75 0 018.75 1h2.5A2.75 2.75 0 0114 3.75v.443c.572.055 1.14.122 1.706.2C17.053 4.582 18 5.75 18 7.07v3.469c0 1.126-.694 2.191-1.83 2.54-1.952.599-4.024.921-6.17.921s-4.219-.322-6.17-.921C2.694 12.73 2 11.665 2 10.539V7.07c0-1.321.947-2.489 2.294-2.676A41.047 41.047 0 016 4.193V3.75zm6.5 0v.325a41.622 41.622 0 00-5 0V3.75c0-.69.56-1.25 1.25-1.25h2.5c.69 0 1.25.56 1.25 1.25zM10 10a1 1 0 00-1 1v.01a1 1 0 001 1h.01a1 1 0 001-1V11a1 1 0 00-1-1H10z"
                clip-rule="evenodd"
              />
              <path
                d="M3 15.055v-.684c.*************.39.142 2.092.642 4.313.987 6.61.987 2.297 0 4.518-.345 6.61-.987.135-.041.264-.089.39-.142v.684c0 1.347-.985 2.53-2.363 2.686a41.454 41.454 0 01-9.274 0C3.985 17.585 3 16.402 3 15.055z"
              />
            </svg>
            {{ org?.org_name || 'Физ.лицо' }}
          </div>
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-200">
            <!-- Heroicon name: mini/map-pin -->
            <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path
                fill-rule="evenodd"
                d="M9.69 18.933l.003.001C9.89 19.02 10 19 10 19s.11.02.308-.066l.002-.001.006-.003.018-.008a5.741 5.741 0 00.281-.14c.186-.096.446-.24.757-.433.62-.384 1.445-.966 2.274-1.765C15.302 14.988 17 12.493 17 9A7 7 0 103 9c0 3.492 1.698 5.988 3.355 7.584a13.731 13.731 0 002.273 1.765 11.842 11.842 0 00.976.544l.***************.006.003zM10 11.25a2.25 2.25 0 100-4.5 2.25 2.25 0 000 4.5z"
                clip-rule="evenodd"
              />
            </svg>
            {{ data.client_country }}, {{ data.client_city }}
          </div>
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-200">
            <!-- Heroicon name: mini/currency-dollar -->
            <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path
                d="M10.75 10.818v2.614A3.13 3.13 0 0011.888 13c.482-.315.612-.648.612-.875 0-.227-.13-.56-.612-.875a3.13 3.13 0 00-1.138-.432zM8.33 8.62c.**************.**************.46.284.736.363V6.603a2.45 2.45 0 00-.35.13c-.14.065-.27.143-.386.233-.377.292-.514.627-.514.909 0 .**************.592.**************.128.152z"
              />
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-6a.75.75 0 01.75.75v.316a3.78 3.78 0 011.653.713c.426.33.744.74.925 1.2a.75.75 0 01-1.395.55 1.35 1.35 0 00-.447-.563 2.187 2.187 0 00-.736-.363V9.3c.698.093 1.383.32 1.959.696.787.514 1.29 1.27 1.29 2.13 0 .86-.504 1.616-1.29 2.13-.576.377-1.261.603-1.96.696v.299a.75.75 0 11-1.5 0v-.3c-.697-.092-1.382-.318-1.958-.695-.482-.315-.857-.717-1.078-1.188a.75.75 0 111.359-.636c.08.173.245.376.54.569.313.205.706.353 1.138.432v-2.748a3.782 3.782 0 01-1.653-.713C6.9 9.433 6.5 8.681 6.5 7.875c0-.805.4-1.558 1.097-2.096a3.78 3.78 0 011.653-.713V4.75A.75.75 0 0110 4z"
                clip-rule="evenodd"
              />
            </svg>
            Оборот: <span class="font-semibold pl-1"> {{ (data.orders_sum && Number(data.orders_sum).toLocaleString()) || '--' }} руб.</span>
          </div>
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-200">
            <!-- Heroicon name: mini/calendar -->
            <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path
                fill-rule="evenodd"
                d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z"
                clip-rule="evenodd"
              />
            </svg>
            {{ data.created_at }}
          </div>
        </div>
      </div>
      <div class="mt-5 flex lg:mt-0 lg:ml-4">
        <div class="sm:ml-3 flex ju space-x-3">
          <div>
            <Button icon="pi pi-bars" text type="button" label="Меню" @click="toggleMenu" aria-haspopup="true" aria-controls="overlay_menu" />
            <Menu id="overlay_menu" ref="menu" :model="menuItems" :popup="true" />
          </div>
          <Button @click="saveUpdate" text class="p-button-success" icon="pi pi-save" label="Сохранить" />
          <Button @click="del($event)" text icon="pi pi-times" label="Удалить" class="ml-2 p-button-danger p-button-outlined"></Button>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 gap-5 items-start sm:gap-10 mt-8">
      <div class="mt-2 bcard p-5">
        <ClientData :client="data"></ClientData>
      </div>
      <!-- <div class="bg-white dark:bg-zinc-900 p-2 rounded-md">
    <div>
        <Button @click="() => org = null" v-if="org" label="Удалить организацию" icon="pi pi-minus" class="float-right p-button-sm p-button-secondary"  />
        <Button @click="addOrg" v-else label="Добавить организацию" icon="pi pi-plus" class="float-right p-button-sm p-button-secondary"  />
      </div>
    <div v-for="(field, index) in clientFields" :key="index">
       <div class="mt-3 flex items-center space-x-2">
          <i :class="'pi ' + fieldIcons[field]" />
          <Inplace :closable="true">
              <template #display>
                <span class="text-gray-500 dark:text-gray-200">{{trnColumns(field)}}:</span> <span class="text-gray-500 dark:text-gray-200 font-bold">{{data[field] || 'Редактировать'}}</span>
              </template>
              <template #content>
                  <InputText v-model="data[field]" autoFocus />
              </template>
          </Inplace>
       </div> 
      </div>
  </div> -->

      <!-- <div v-if="org">
    <div class="bg-white dark:bg-zinc-900 p-4 rounded-md /sm:grid /sm:grid-cols-2">
      <div v-for="(field, index) in orgFields" :key="index">
       <div class="mt-3 flex items-center space-x-2">
          <i :class="'pi ' + orgIcons[field]" />
          <Inplace :closable="true">
              <template #display>
                <span class="text-gray-500 dark:text-gray-200">{{trnColumns(field)}}:</span> <span class="text-gray-500 dark:text-gray-200 font-bold">{{org[field] || 'Редактировать'}}</span>
              </template>
              <template #content>
                  <InputText v-model="org[field]" autoFocus />
              </template>
          </Inplace>
       </div> 
      </div>
    </div>
  </div> -->

      <div class="p-5 bcard" v-if="data.orders?.length">
        <div class="max-h-xl overflow-auto rounded-md">
          <ScrollPanel style="width: 100%; height: 600px" class="custom">
            <div class="bg-white dark:bg-zinc-900 mb-5 sshadow-xl shadow-slate-200 dark:shadow-none p-4 rounded-md" v-for="(order, index) in data.orders" :key="index">
              <div class="flex justify-between">
                <a :href="'/orders/' + order.order_id" class="font-bold hover:underline text-gray-500 dark:text-gray-200">#{{ order.order_id }}</a>
                <span class="text-gray-500 dark:text-gray-200 font-bold">{{ order.lastSnapshot?.body?.orderDateTime || order.order_datetime }}</span>
              </div>
              <div class="text-gray-600 dark:text-gray-200 dark:text-gray-200 flex justify-between space-x-4 mt-2">
                <div>
                  <div>
                    <span>Статус:</span> <span :class="getColorByStatus(order.order_status)" class="py-1 px-2 rounded-md">{{ order.order_status }}</span>
                  </div>
                  <div>
                    <span>Оплата:</span> <span>{{ order.order_payment }}</span>
                  </div>
                  <div>
                    <span>Доставка:</span> <span>{{ order.order_shipping }}</span>
                  </div>
                  <div>
                    <span>Примечание:</span>
                    <div v-if="order.order_desc" class="mx-2 bg-gray-100 dark:bg-zinc-700 p-2 rounded-md">
                      <span class="">{{ order.order_desc }}</span>
                    </div>
                  </div>
                  <div class="mt-2">
                    <span>Пометки по заказу:</span> <span class="text-yellow-500 italic">{{ order.order_notice }}</span>
                  </div>
                </div>

                <div>
                  <div>
                    <span>Сумма:</span> <span class="dark:text-zinc-100 font-semibold">{{ Number(order.order_price).toLocaleString() }}</span>
                    руб.
                  </div>
                  <div>
                    <span>Скидка:</span> <span class="dark:text-zinc-100 font-semibold">{{ Number(order.order_coupons?.personal).toLocaleString() }}</span
                    >%.
                  </div>
                  <div>
                    <span>Доставка:</span> <span class="dark:text-zinc-100 font-semibold">{{ Number(order.order_shippingprice).toLocaleString() }}</span> руб.
                  </div>
                  <div class="mt-3 bg-yellow-100 dark:bg-orange-600/50 px-2 py-1 rounded">
                    <span class="font-bold">Итого:</span>
                    <span class="text-slate-500 dark:text-zinc-200 font-bold">{{ Number(order.order_price + order.order_shippingprice).toLocaleString() }}</span> руб.
                  </div>
                </div>
              </div>
              <hr class="my-3" />
              <div class="mt-3 flex items-start justify-between flex-wrap">
                <div v-if="order.lastSnapshot?.body?.client">
                  <span class="text-gray-600 dark:text-gray-200 dark:text-gray-200 font-bold">Получатель:</span>
                  <div v-for="(field, index) in clientFields" :key="index">
                    <span class="text-gray-500 dark:text-gray-200">{{ trnColumns(field) }}:</span>
                    <span class="text-gray-500 dark:text-gray-200 font-bold">{{ order.lastSnapshot.body.client[field] }}</span>
                  </div>
                </div>
                <div v-if="order.lastSnapshot?.body?.items?.length">
                  <Listbox :options="order.lastSnapshot.body.items" :filter="true" optionLabel="prod_analogsku" listStyle="max-height:250px" style="width: 15rem" filterPlaceholder="Поиск по OEM">
                    <template #option="slotProps">
                      <div _class="flex items-center">
                        <!-- <img :src="`https://mirsalnikov.ru/data/rti/${slotProps.option.prod_analogsku}.jpg`" width="40" class="mr-2" /> -->
                        <div>
                          <router-link :to="'/products/' + slotProps.option.prod_id" class="text-slate-500 dark:text-gray-300">{{ slotProps.option.prod_analogsku }}</router-link> ({{
                            slotProps.option.prod_sku
                          }}) x
                          <b>{{ slotProps.option.orderCount }}</b>
                        </div>
                      </div>
                    </template>
                  </Listbox>
                </div>
                <!-- <Accordion :multiple="true" lazy :activeIndex="0">
                  <AccordionTab header="Получатель">
                      <div v-for="(field, index) in clientFields" :key="index">
                      <span class="text-gray-500 dark:text-gray-200">{{trnColumns(field)}}:</span> <span class="text-gray-500 dark:text-gray-200 font-bold">{{order.lastSnapshot.body.client[field]}}</span>
                    </div>
                    </AccordionTab>
                  <AccordionTab v-if="order.lastSnapshot?.body?.items?.length"  header="Состав заказа">
                    <Listbox :options="order.lastSnapshot.body.items" :filter="true" optionLabel="prod_analogsku" listStyle="max-height:250px" style="width:15rem" filterPlaceholder="Поиск по OEM">
                      <template #option="slotProps">
                          <div _class="flex items-center">
                              <div>{{slotProps.option.prod_analogsku}} ({{slotProps.option.prod_sku}}) x {{slotProps.option.orderCount}}</div>
                          </div>
                      </template> 
                  </Listbox>
                  </AccordionTab>
              </Accordion> -->
              </div>
            </div>
            <Divider />
          </ScrollPanel>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-2 bg-white dark:bg-zinc-900 gap-4 p-3 rounded-md mt-10" v-if="!countersLoading">
      <div>
        <TotalSalesChart title="Активность по сумме" :salesCounters="salesCounters" :prev_salesCounters="prev_salesCounters" />
      </div>
      <div>
        <OrdersChart :prev="true" title="Активность по заказам" :ordersCounters="ordersCounters" :prev_ordersCounters="prev_ordersCounters" />
      </div>
    </div>

    <Dialog dismissableMask modal header="Коммерческое предложение" v-model:visible="makeOfferDialogShow" :breakpoints="{ '960px': '75vw', '640px': '90vw' }" :style="{ width: '80vw' }">
      <!-- WhosaleMde: true,
        productData: [], //{id,qty}
        shippingIndex: '',
        // shippingType: 'standard' | 'express',
        ///// shippingPrice: number | undefined,
        countryId: 643,
        filename: '',
        offerExp: '' -->

      <div>
        <h5 class="text-gray-600 dark:text-gray-200 dark:text-gray-200 font-bold mb-2">Шаблон документа*</h5>
        <SelectButton v-model="offerParams.filename" :options="documents" aria-labelledby="offer_ship" />
      </div>

      <div class="mt-5">
        <h5 class="text-gray-600 dark:text-gray-200 dark:text-gray-200 font-bold mb-2" id="offer_ship">Доставка*</h5>
        <SelectButton
          v-model="offerParams.shippingType"
          optionLabel="name"
          :options="[
            { name: 'Почта РФ', value: 'standard' },
            { name: 'EMS', value: 'express' }
          ]"
          aria-labelledby="offer_ship"
        />
      </div>

      <div class="mt-5">
        <h5 class="text-gray-600 dark:text-gray-200 dark:text-gray-200 font-bold mb-2">Код страны назначения*</h5>
        <InputNumber :format="false" v-model="offerParams.countryId"></InputNumber>
      </div>

      <div class="mt-5">
        <h5 class="text-gray-600 dark:text-gray-200 dark:text-gray-200 font-bold mb-2" id="offer_zip">Индекс назначения*</h5>
        <InputNumber :format="false" id="offer_zip" v-model="offerParams.shippingIndex"></InputNumber>
      </div>

      <div class="mt-5">
        <h5 class="text-gray-600 dark:text-gray-200 dark:text-gray-200 font-bold mb-2">Указать стоимость доставки (необязательно)</h5>
        <InputNumber mode="currency" currency="RUB" locale="ru-RU" v-model="offerParams.shippingPrice"></InputNumber>
      </div>

      <Divider />

      <div class="transition-all bg-gray-50 ease-in-out duration-300 p-2 px-4 rounded-md mt-5">
        <div class="flex justify-center">
          <AutoComplete
            dataKey="prod_id"
            :forceSelection="false"
            v-model="RKfilterModel"
            :suggestions="filteredProds"
            @item-select="addToRK($event)"
            @complete="findRKitemHandler($event)"
            placeholder="Добавить товар"
            _dropdown="true"
          >
            <template #item="slotProps">
              <div class="flex items-center justify-left space-x-5">
                <!-- <img class="w-16 rounded-md shadow" :alt="slotProps.item.prod_img" :src="'https://mirsalnikov.ru/data/rti/' + slotProps.item.prod_img + '.jpg'" /> -->
                <div class="font-bold text-gray-700">{{ slotProps.item.prod_analogsku }}</div>
                <div class="text-gray-700">{{ slotProps.item.prod_sku }}</div>
                <div class="text-gray-600 dark:text-gray-200">{{ slotProps.item.prod_size }}</div>
                <div class="text-slate-800">Склад: {{ slotProps.item.prod_count }}</div>
              </div>
            </template>
          </AutoComplete>
        </div>

        <Divider />

        <div v-for="(item, index) in offerProducts" :key="index">
          <div class="bg-gray-200 text-gray-600 dark:text-gray-200 dark:text-gray-200 font-semibold rounded-md p-2 flex justify-between items-center space-x-5 mt-3">
            <!-- <div>
                    <img class="w-20 rounded-md shadow" :alt="item.product.prod_img" :src="'https://mirsalnikov.ru/data/rti/' + item.product.prod_img + '.jpg'" />
                </div> -->
            <div>
              <div>{{ item.product.prod_analogsku }} / {{ item.product.prod_sku }}</div>
              <!-- <div>{{item.product.prod_sku}}</div> -->
              <div>{{ item.product.prod_manuf }} / {{ item.product.prod_size }} / {{ item.product.prod_type }}</div>
              <div class="text-slate-800">Наличие: {{ item.product.prod_count }}</div>
            </div>
            <div class="">
              <InputNumber
                @input="RKinputChangeHandler($event, item.id)"
                inputClass="w-20"
                v-model="item.qty"
                showButtons
                buttonLayout="horizontal"
                :step="1"
                :min="0"
                :allowEmpty="false"
                autofocus
                decrementButtonClass="p-button-secondary p-1"
                incrementButtonClass="p-button-secondary p-1"
                incrementButtonIcon="pi pi-plus"
                decrementButtonIcon="pi pi-minus"
              />
            </div>
          </div>
          <Divider />
        </div>
      </div>

      <template #footer>
        <Button text :loading="offerLoading" label="Сформировать" icon="pi pi-check" @click="makeOffer" autofocus />
      </template>
    </Dialog>

    <div v-if="reconactDialogShow">
      <Dialog v-model:visible="reconactDialogShow" modal header="Акт сверки" :style="{ width: '50%' }">
        <div>
          <Reconact @on-print="reconactPrint" :client-id="data.client_id" />
        </div>
      </Dialog>
    </div>

    <Dialog modal header="Сбросить пароль" v-model:visible="restorePasswordDialog">
      <div class="my-5">
        <div class="text-gray-500 dark:text-gray-200 font-semibold">Укажите пароль или оставьте поле пустым для генерации пароля</div>
        <div class="text-yellow-600 italic mt-2">Пароль будет выслан на электронную почту клиента.</div>

        <div class="mt-5">
          <span class="p-float-label">
            <InputText id="__newpassword" type="text" v-model="resetPasswordValue" />
            <label for="__newpassword">Новый пароль</label>
          </span>
        </div>
      </div>

      <template #footer>
        <Button label="Продолжить" icon="pi pi-check" @click="resetPassword" autofocus />
      </template>
    </Dialog>
  </div>
</template>
