<script setup lang="ts">
import Button from 'primevue/button'
import { ref } from 'vue'

// import { defineProps } from 'vue'

const props = defineProps({
  text: {
    type: String,
    required: true
  }
})

const showFullText = ref(false)
const selected = ref(false)
</script>
<template>
  <div @click="() => (selected = !selected)">
    <div v-if="!showFullText" class="overflow-hidden transition-all duration-500">
      <div v-html="text.slice(0, 1500)"></div>
      <span>....</span>
    </div>
    <div class="p-2" v-if="text.length > 1500">
      <div v-html="text" v-show="showFullText" class="overflow-hidden transition-all duration-500"></div>
      <div class="flex justify-end"><Button text @click="showFullText = !showFullText" :label="(showFullText ? 'Скрыть' : 'Показать') + ' полный текст'" /></div>
    </div>
  </div>
</template>
