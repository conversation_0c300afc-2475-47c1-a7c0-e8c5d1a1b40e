<script setup lang="ts">
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import ProgressSpinner from 'primevue/progressspinner'
import ScrollPanel from 'primevue/scrollpanel'
import { ref } from 'vue'
import ClientMailBoxHtml from './ClientMailBoxHtml.vue'
import TabMenu from 'primevue/tabmenu'

interface EmailItem {
  html: string
  date: Date | string
  cc: unknown
  subject: string
  type: 'in' | 'out'
}

const props = defineProps({
  clientemail: String
})

const list = ref<EmailItem[]>([])
const type = ref<'in' | 'out'>('in')

const activeTab = ref(0)
const items = ref([
  {
    label: 'Исходящие',
    icon: 'pi pi-fw pi-arrow-right'
  },
  {
    label: 'Входящие',
    icon: 'pi pi-fw pi-arrow-left'
  }
])

const { isLoading, isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryKey: [activeTab, props.clientemail],
  queryFn: loadMailBox,
  //   onError: (e) => apiErrorToast(e),
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
  refetchOnMount: false,
  retry: false,
  onSuccess: (data) => {
    list.value = data
  }
})

async function loadMailBox() {
  const qs = new URLSearchParams()
  qs.set('clientemail', String(props.clientemail))
  qs.set('type', activeTab.value ? 'in' : 'out')

  const res = await api('/service/emails/box?' + qs.toString())

  if (res.ok) {
    return res.body
  } else return new Error(res.statusText)
}
</script>

<template>
  <div>
    <div>
      <TabMenu v-model:activeIndex="activeTab" :model="items" />
      <ScrollPanel class="custombar1" style="width: 100%; height: 400px">
        <div v-if="isFetching">
          <div class="flex flex-col items-center justify-center">
            <div class="text-slate-400 font-bold text-lg">Загрузка сообщений</div>
            <ProgressSpinner />
          </div>
        </div>
        <div class="space-y-10" v-else>
          <div class="shadow-lg shadow-slate-200 dark:shadow-none" :key="index" v-for="(item, index) in list">
            <div class="flex justify-between dark:bg-zinc-900 p-2 rounded-md mr-3">
              <div>
                Дата:
                <span class="font-semibold">
                  {{ new Date(item.date).toLocaleString() }}
                </span>
              </div>
              <div>
                Тема:
                <span class="font-semibold">
                  {{ item.subject }}
                </span>
              </div>
            </div>
            <div class="p-2 !dark:bg-zinc-700">
              <ClientMailBoxHtml :text="item.html" />
            </div>
          </div>
        </div>
      </ScrollPanel>
    </div>
  </div>
</template>
