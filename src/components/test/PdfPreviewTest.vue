<template>
  <div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">Тест предпросмотра PDF</h1>
    
    <div class="space-y-6">
      <!-- Тест с простым markdown -->
      <div class="bg-white border border-gray-200 rounded-lg p-4">
        <h2 class="text-lg font-semibold mb-3">Простой markdown с PDF ссылкой</h2>
        <MarkdownRenderer 
          :content="simpleMarkdown"
          :open-links-in-new-tab="true"
        />
      </div>

      <!-- Тест с множественными ссылками -->
      <div class="bg-white border border-gray-200 rounded-lg p-4">
        <h2 class="text-lg font-semibold mb-3">Множественные ссылки</h2>
        <MarkdownRenderer 
          :content="multipleLinksMarkdown"
          :open-links-in-new-tab="true"
        />
      </div>

      <!-- Тест с реальными PDF ссылками -->
      <div class="bg-white border border-gray-200 rounded-lg p-4">
        <h2 class="text-lg font-semibold mb-3">Реальные PDF документы</h2>
        <MarkdownRenderer
          :content="realPdfMarkdown"
          :open-links-in-new-tab="true"
        />
      </div>

      <!-- Имитация сообщения агента -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h2 class="text-lg font-semibold mb-3 text-blue-800">Пример ответа агента</h2>
        <div class="bg-white border border-gray-200 rounded-lg px-4 py-3 shadow-sm">
          <MarkdownRenderer
            :content="agentResponseMarkdown"
            :open-links-in-new-tab="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import MarkdownRenderer from '@/components/common/MarkdownRenderer.vue'

// Тестовые данные
const simpleMarkdown = `
# Коммерческое предложение

Уважаемый клиент!

Предлагаем вашему вниманию наше коммерческое предложение.

**Документы:**
- [Скачать PDF предложение](https://example.com/proposal.pdf)
- [Обычная ссылка](https://example.com)

Спасибо за внимание!
`

const multipleLinksMarkdown = `
## Документооборот

В рамках нашего сотрудничества предоставляем следующие документы:

1. [Коммерческое предложение](https://example.com/commercial-offer.pdf) - основные условия
2. [Техническое задание](https://example.com/technical-spec.pdf) - детальные требования  
3. [Договор поставки](https://example.com/contract.pdf) - юридические аспекты
4. [Сайт компании](https://example.com) - дополнительная информация
5. [Прайс-лист](https://example.com/price-list.pdf) - актуальные цены

Все PDF документы доступны для предпросмотра прямо в интерфейсе.
`

const realPdfMarkdown = `
## Примеры реальных документов

Для демонстрации функциональности предпросмотра:

- [Пример PDF документа](https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf)
- [Mozilla PDF.js demo](https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf)
- [Обычная веб-страница](https://www.google.com)

> **Примечание:** Рядом с PDF ссылками должна появиться кнопка с иконкой глаза для предпросмотра.
`

const agentResponseMarkdown = `
# Коммерческое предложение для ООО "Технологии"

Уважаемые коллеги!

Благодарим за обращение в нашу компанию. На основе ваших требований подготовили персональное коммерческое предложение.

## 📋 Документы для ознакомления

**Основные документы:**
- [Коммерческое предложение](https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf) - детальное описание услуг и цен
- [Техническое задание](https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf) - технические требования и спецификации

**Дополнительные материалы:**
- [Сертификаты качества](https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf)
- [Портфолио проектов](https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf)

## 💰 Стоимость

| Услуга | Цена | Срок |
|--------|------|------|
| Разработка | 150 000 ₽ | 2 недели |
| Внедрение | 50 000 ₽ | 1 неделя |
| Поддержка | 15 000 ₽/мес | Постоянно |

## 📞 Контакты

Для уточнения деталей свяжитесь с нами:
- Email: <EMAIL>
- Телефон: +7 (495) 123-45-67

**Предложение действительно до:** 31.12.2024

---

*Все PDF документы можно просмотреть прямо в интерфейсе, нажав на кнопку с иконкой глаза рядом со ссылкой.*
`
</script>

<style scoped>
/* Дополнительные стили для тестовой страницы */
</style>
