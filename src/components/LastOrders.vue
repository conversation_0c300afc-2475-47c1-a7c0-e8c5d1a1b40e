<script lang="ts" setup>
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import Column from 'primevue/column'
import DataTable from 'primevue/datatable'

const URL = new URLSearchParams()

const { isFetching, isError, data, error, refetch } = useQuery({
  queryKey: ['unOrders'], //[route.fullPath],
  queryFn: async () => await loadData(),
  refetchOnMount: false,
  refetchOnWindowFocus: true,
  refetchOnReconnect: false
})

async function loadData() {
  !URL.has('page') && URL.set('page', '1')
  !URL.has('limit') && URL.set('limit', '10')
  !URL.has('filters') && URL.set('filters', '{"order_status":{"value":"Не обработан","matchMode":"equals"}}')

  const res = await api('/cpan/orders/list/' + '?' + URL.toString())

  return res?.body
}
</script>
<template>
  <div>
    <div class="text-gray-600 dark:text-gray-200 dark:text-gray-200 ttext-black font-semibold">Не обработанные заказы</div>
    <div class="mt-3">
      <DataTable table-class="table-default" :loading="isFetching" :value="data?.orders?.data">
        <!-- <Column style="width: 30px" field="order_locale" header="">
          <template #body="slotProps">
            <div class="p-1 rounded-md bg-gray-600 dark:bg-gray-800 text-white font-semibold text-sm text-center">
              {{ slotProps.data.order_locale == 'en' ? 'RUMI' : 'RTI' }}
            </div>
          </template>
        </Column> -->
        <Column style="width: 40px" field="order_id" header="ID">
          <template #body="{ data }">
            <router-link class="hover:underline font-bold" :to="'/orders/' + data.order_id">{{ data.order_id }}</router-link>
          </template>
        </Column>
        <Column style="width: 40px" field="order_datetime" header="Дата">
          <template #body="{ data }">
            <router-link class="hover:underline" :to="'/orders/' + data.order_id">{{ data.order_datetime }}</router-link>
          </template>
        </Column>
        <Column style="width: 200px" field="order_client" header="Получатель">
          <template #body="{ data }">
            <router-link class="hover:underline" :to="'/clients/' + data.snapshots?.[0]?.body?.client?.client_id">{{ data.snapshots?.[0]?.body?.client?.client_name }}</router-link>
          </template>
        </Column>
        <Column style="width: 40px" field="order_shipping" header="Доставка"> </Column>
        <Column style="width: 140px" field="order_price" header="Сумма">
          <template #body="slotProps">
            <div class="font-bold">{{ Number((Number(slotProps.data.order_price) + Number(slotProps.data.order_shippingprice)).toFixed(2)).toLocaleString() }} руб.</div>
          </template>
        </Column>
        <!-- <Column field="name" header="Name"></Column> -->
        <!-- <Column field="category" header="Category"></Column> -->
        <!-- <Column field="quantity" header="Quantity"></Column> -->
      </DataTable>
    </div>
  </div>
</template>
