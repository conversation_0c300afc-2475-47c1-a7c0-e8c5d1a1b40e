<script setup lang="ts">
import { ref, watch, provide, onMounted } from 'vue'

import { use, registerLocale } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'

import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { SunburstChart } from 'echarts/charts'
import { VisualMapComponent } from 'echarts/components'
import VChart, { THEME_KEY } from 'vue-echarts'
import { TreemapChart } from 'echarts/charts'
import { BarChart, LineChart } from 'echarts/charts'
import { GridComponent, DataZoomComponent } from 'echarts/components'
import { ToolboxComponent } from 'echarts/components'
// import Tree from 'primevue/tree'
import { useQuery } from '@tanstack/vue-query'

import { slate, sky, rose, orange, indigo, zinc, blue, red, gray, green } from 'tailwindcss/colors'
import type { EChartsOption, EChartsType } from 'echarts'
import { api } from '@/lib/_api'
import dayjs from 'dayjs'
import('dayjs/locale/ru')
import 'echarts/i18n/langRU'

use([
  SunburstChart,
  TreemapChart,
  DataZoomComponent,
  ToolboxComponent,
  BarChart,
  LineChart,
  GridComponent,
  VisualMapComponent,
  CanvasRenderer,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])
provide(THEME_KEY, 'light')

const monthNumbers = {
  Январь: '01',
  Февраль: '02',
  Март: '03',
  Апрель: '04',
  Май: '05',
  Июнь: '06',
  Июль: '07',
  Август: '08',
  Сентябрь: '09',
  Октябрь: '10',
  Ноябрь: '11',
  Декабрь: '12'
}

function transformData(data) {
  const result = []

  for (const year in data) {
    const yearData = data[year]

    for (const month in yearData) {
      const monthNumber = monthNumbers[month]

      result.push([`${year}-${monthNumber}-01`, yearData[month]])
    }
  }

  //console.log('shipping result:', result)

  return result
}

function separateShippingData(data) {
  const kurierTypes = ['Курьер', 'Курьер KCE', 'Курьер КСЭ', 'СДЭК']
  const pochtaTypes = ['Почта РФ', 'undefined', 'Не указано']

  const result = {
    'Почта РФ': {},
    'Курьер': {},
  }

  // Process each shipping type
  Object.entries(data).forEach(([shippingType, yearData]) => {
    // Determine which group this shipping type belongs to
    const targetGroup = kurierTypes.includes(shippingType) ? 'Курьер' : 'Почта РФ'

    // For each year in the shipping type data
    Object.entries(yearData).forEach(([year, monthData]) => {
      if (!result[targetGroup][year]) {
        result[targetGroup][year] = {}
      }

      // For each month, sum up the values
      Object.entries(monthData).forEach(([month, value]) => {
        if (!result[targetGroup][year][month]) {
          result[targetGroup][year][month] = 0
        }
        result[targetGroup][year][month] += value
      })
    })
  })

  return result
}

async function loadData() {
  //   return demoData

  const kurierTypes = ['Курьер', 'Курьер KCE', 'Курьер КСЭ', 'СДЭК']
  const pochtaTypes = ['Почта РФ', 'undefined', 'Не указано']
  const allTypes = [...kurierTypes, ...pochtaTypes]

  const res = await api('/cpan/statistics/byorders', {
    data: {
      groupby: ['order_shipping', 'year', 'month'],
      shippingtypes: allTypes, //['Почта РФ', 'Курьер'],
      sumby: 'order_price' // 'orderSum'
    }
  })

  //console.log('response data:', res.body)

  return res.ok ? res.body : res.message
}

function onSuccessLoadData(data) {
  data = separateShippingData(data)

  const series: EChartsOption['series'] = Object.keys(data).map((categoryTitle) => {
    return {
      name: categoryTitle,
      type: 'line',
      smooth: true,
      //   symbolSize: 8,
      emphasis: {
        focus: 'series'
      },
      //   areaStyle: {
      //     shadowBlur: 1,
      //     opacity: 10
      //   },
      //   stackStrategy: 'all',
      data: transformData(data[categoryTitle]),
      endLabel: {
        show: true,
        formatter: '{a}',
        distance: 10
      }
    }
  })

  //   series.map((s) => (option.value.legend.selected[s.name] = false))
  //   option.value.legend.selected['TTO'] = true

  option.value.series = series
}

const { isFetching, isError, data, error, refetch, isLoading } = useQuery({
  queryKey: ['shippingTypeChart'],
  queryFn: async () => await loadData(),
  onSuccess: async (data) => onSuccessLoadData(data),
  refetchOnMount: true,
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
  retry: 2
})

const option = ref<EChartsOption>({
  tooltip: {
    trigger: 'axis',
    formatter: (params) => {
      params = [params].flat().sort((a, b) => b.data[1] - a.data[1])
      const [date] = params[0]?.data
      const ss = params
        .map((item) => {
          const [date, value] = item.data
          const { seriesName, marker } = item
          return `<div class="text-sm">${marker} ${seriesName}:  <span class="font-semibold text-zinc-900">${Number(value).toLocaleString()} руб</span></div>`
        })
        .join(' ')

      return `<div class="text-sm font-semibold mb-1">${dayjs(date).locale('ru').format('YYYY MMMM')}:</div> ${ss}`
    }
  },
  grid: {
    show: true,
    // left: '20%',
    top: '10%',
    containLabel: true
  },
  legend: {
    left: 'center',
    // bottom: 'bottom',
    orient: 'horizontal',
    itemGap: 15,
    textStyle: {
      fontWeight: 'bold',
      fontSize: '15px',
      color: sky[600]
    },
    inactiveColor: zinc[600],

    selected: {}
  },
  //   title: {
  //     left: 'right',
  //     text: 'Large Ara Chart'
  //   },
  toolbox: {
    feature: {
      dataZoom: {
        yAxisIndex: 'all',
        show: true,
        filterMode: 'weakFilter'
      },
      restore: {},
      saveAsImage: {}
    }
  },
  xAxis: {
    type: 'time',
    // boundaryGap: false,
    splitLine: {
      show: true
    },

    axisLabel: {
      margin: 10,
      fontSize: 14
    }
  },
  yAxis: {
    type: 'value',
    boundaryGap: [0, '100%'],
    max: function (value) {
      return value.max + value.max * 0.09
    }
  },
  dataZoom: [
    {
      type: 'inside',
      //   start: 35,
      start: 0,
      //   startValue: '2021-01-01',
      end: 100
    },
    {
      start: 0,
      //   startValue: '2021-01-01',
      end: 100
    }
  ],
  series: []
})
</script>

<template>
  <div>
    <div class="text-center text-lg text-gray-600 dark:text-gray-200 dark:text-gray-200 font-semibold">Оборот по доставке</div>
    <div v-if="!isLoading" :class="option.series?.length ? 'h-[400px] ' : ''">
      <v-chart style="height: 400px" :init-options="{ locale: 'ru' }" :loading="isFetching" class="chart mt-3" :option="option" autoresize />
    </div>
  </div>
</template>
