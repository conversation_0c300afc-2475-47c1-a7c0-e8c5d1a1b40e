<script lang="ts" setup>
import { api } from '@/lib/_api'
import { useMutation } from '@tanstack/vue-query'
import dayjs from 'dayjs'
import Button from 'primevue/button'
import Calendar from 'primevue/calendar'
import ConfirmDialog from 'primevue/confirmdialog'
import InputSwitch from 'primevue/inputswitch'
import { useConfirm } from 'primevue/useconfirm'
import { ref } from 'vue'

const confirm = useConfirm()
const startDate = ref<Date>()
const endDate = ref<Date>()
const toEmail = ref(false)

const { data, isSuccess, isError, mutateAsync, isLoading } = useMutation({
  mutationFn: async () => {
    const qs = new URLSearchParams()

    qs.set(
      'dates',
      `${encodeURIComponent(startDate.value ? dayjs(startDate.value).format('YYYY-MM-DD HH:mm:ss') : '')},${
        endDate.value ? encodeURIComponent(dayjs(endDate.value).format('YYYY-MM-DD HH:mm:ss')) : ''
      }`
    )

    toEmail.value && qs.set('toEmail', '1')

    const url = `${import.meta.env.DEV ? 'http://localhost:3333' : 'https://api.mirsalnikov.ru'}/statistics/full?${qs.toString()}`

    const response = await fetch(url)

    if (toEmail.value) {
      return
    }

    const blob = await response.blob()

    const contentDisposition = response.headers.get('content-disposition')
    const filename = contentDisposition || `rti_report_${startDate.value?.toLocaleString() || '2020'}-${endDate.value?.toLocaleDateString() || new Date().getFullYear()}`

    const urlObject = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = urlObject
    link.download = filename + '.xlsx'
    // link.click()

    return link
  },
  onSuccess(link: HTMLAnchorElement) {
    link.click()
  }
})

async function downloadHandler() {
  //console.log({ startDate: startDate.value, endDate: endDate.value })
  await mutateAsync()
}

function openConfirm() {
  confirm.require({
    group: 'templating',
    header: 'Выгрузка отчета',
    message: '',
    acceptLabel: 'Сгенерировать',
    rejectLabel: 'Закрыть',
    icon: 'pi pi-exclamation-circle',
    acceptIcon: 'pi pi-check',
    rejectIcon: 'pi pi-times',
    rejectClass: 'p-button-sm p-button-text',
    acceptClass: 'p-button-text p-button-success p-button-sm',
    accept: async () => {
      await downloadHandler()
    },
    reject: () => {}
  })
}
</script>

<template>
  <div>
    <ConfirmDialog group="templating">
      <template #message="slotProps">
        <div>
          <div class="text-gray-500 font-semibold">Указать период <span class="text-xs text-yellow-500">(необязательно)</span></div>
          <div class="flex flex-column align-items-center w-full gap-3 border-bottom-1 surface-border">
            <div class="flex space-x-4 items-end mt-2">
              <div>
                <div>От:</div>
                <Calendar v-model="startDate" dateFormat="dd-mm-yy" />
              </div>
              <div>
                <div>По:</div>
                <Calendar v-model="endDate" dateFormat="dd-mm-yy" />
              </div>
              <!-- <Button @click="downloadHandler" icon="pi pi-save" label="Скачать" /> -->
            </div>
          </div>
          <div class="flex space-x-5 justify-center mt-7 items-center w-full">
            <span>Отправить результат на E-mail </span>
            <InputSwitch v-model="toEmail" />
          </div>
          <div class="italic text-sm text-slate-500 text-right mt-7">Время генерации: <b>~ 4</b> мин</div>
        </div>
      </template>
    </ConfirmDialog>

    <Button :loading="isLoading" @click="openConfirm" icon="pi pi-download" text label="Выгрузить xlsx" />
  </div>
</template>
