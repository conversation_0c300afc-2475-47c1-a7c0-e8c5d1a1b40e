<script setup lang="ts">
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import dayjs from 'dayjs'
import ScrollPanel from 'primevue/scrollpanel'
import { defineProps, ref } from 'vue'

export interface Stat {
  description: string
  hangupcause: string
  callstart: string
  currency: string
  cost: number
  disposition: string
  billcost: number
  billseconds: number
  sip: string
  id: string
  from: any
  to: any
}

export interface Data {
  status: string
  start: string
  end: string
  stats: Stat[]
}

enum callStatusEnum {
  'answered' = 'разговор',
  'busy' = 'занято',
  'cancel' = 'отменен',
  'no answer' = 'без ответа',
  'call failed' = 'не удался',
  'no money' = 'нет средств, превышен лимит',
  'unallocated number' = 'номер не существует',
  'no limit' = 'превышен лимит',
  'no day limit' = 'превышен дневной лимит',
  'line limit' = 'превышен лимит линий',
  'no money, no limit' = 'превышен лимит'
}

const zadarmastats = ref()

const loadZadarmaStats = async () => {
  const { body } = await api('/service/zadarma', {
    method: 'POST',
    data: {
      method: '/v1/statistics/',
      payload: {
        // skip: 1,
        end: dayjs().add(1, 'hour').format('YYYY-MM-DD 23:59:59'),
        start: dayjs().subtract(2, 'day').format('YYYY-MM-DD 23:59:59'), // hh:mm:ss
        limit: 50
      }
    }
  })

  zadarmastats.value = body

  const { body: clients } = await api('/service/clients/byphones', {
    method: 'POST',
    data: {
      phones: [
        ...new Set(
          body.stats?.map((i) => i.to),
          body.stats?.map((i) => i.from)
        )
      ]
    }
  })

  zadarmastats.value.stats = zadarmastats.value.stats?.map((item) => {
    let fnd = clients.find((x) => x.client_phone.replace(/\D/gm, '') == item.to || x.client_phone.replace(/\D/gm, '') == item.from)
    return fnd ? { ...item, ...fnd } : item
  })

  zadarmastats.value.stats.reverse()

  return zadarmastats.value
  return body
}

const { isLoading, isError, data, error } = useQuery({
  queryKey: ['zadarmaCounters'],
  queryFn: async () => await loadZadarmaStats(),
  refetchOnMount: false,
  refetchOnWindowFocus: true,
  refetchOnReconnect: true
  // retry: 3,
})

const incphone = '74951335935'

const incIcon = `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="text-green-400 w-7 h-7">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.25 9.75v-4.5m0 4.5h4.5m-4.5 0l6-6m-3 18c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 014.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 00-.38 1.21 12.035 12.035 0 007.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293a1.125 1.125 0 011.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 01-2.25 2.25h-2.25z" />
                    </svg>`
const outIcon = `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="text-gray-500 dark:text-gray-200 w-7 h-7">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 3.75v4.5m0-4.5h-4.5m4.5 0l-6 6m3 12c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 014.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 00-.38 1.21 12.035 12.035 0 007.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293a1.125 1.125 0 011.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 01-2.25 2.25h-2.25z" />
                    </svg>`
const cancelIcon = `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="text-red-400 w-7 h-7">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 3.75L18 6m0 0l2.25 2.25M18 6l2.25-2.25M18 6l-2.25 2.25m1.5 13.5c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 014.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 00-.38 1.21 12.035 12.035 0 007.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293a1.125 1.125 0 011.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 01-2.25 2.25h-2.25z" />
                        </svg>`
const okIcon = `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="text-gray-500 dark:text-gray-200 w-7 h-7">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                    </svg>`

const statusIcon = {
  'no answer': cancelIcon,
  'call failed': cancelIcon,
  'cancel': cancelIcon,
  'unallocated number': cancelIcon
}

const getIcon = (status, from) => (from == incphone && !statusIcon[status] ? outIcon : statusIcon[status] || incIcon)
</script>

<template>
  <div class="text-gray-600 dark:text-gray-200 dark:text-gray-200 ttext-black font-semibold">Последние звонки</div>
  <ScrollPanel style="width: 100%; height: 250px" class="custom">
    <div v-if="data?.stats">
      <div class="flow-root">
        <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
          <li v-for="(item, index) in data.stats" :key="index" class="py-3 sm:py-4">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="flex items-center space-x-2 mr-2" v-html="getIcon(item.disposition, item.from)"></div>
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 truncate dark:text-white">
                  <div>{{ item[item.to != incphone ? 'to' : 'from'] }}</div>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-200 truncate">
                  <router-link v-if="item.client_id" :to="'/clients/' + item.client_id"
                    ><div class="w-20 text-slate-700 dark:text-zinc-400">({{ item.client_name }})</div></router-link
                  >
                  <div v-else class="w-20 text-gray-600 dark:text-gray-200">(Неизвестный)</div>
                </div>
              </div>
              <div class="text-sm">{{ item.description }}</div>

              <div class="inline-flex space-x-3 items-center text-base font-semibold text-gray-900 dark:text-white">
                <div>
                  <span class="bg-gray-100 dark:bg-zinc-800  p-1 px-2 rounded-md mx-auto">{{ callStatusEnum[item.disposition] }}</span>
                </div>
                <div>
                  <span class="bg-gray-100 dark:bg-zinc-800 p-1 px-2 rounded-md mx-auto">{{ item.billseconds.toFixed() }} сек.</span>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-200 bg-yellow-50 dark:bg-orange-700 p-2 rounded-md">{{ item.callstart }}</div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </ScrollPanel>
</template>
