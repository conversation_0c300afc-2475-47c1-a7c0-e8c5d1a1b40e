<template>
  <Dialog
    v-model:visible="visible"
    modal
    :header="'Создание счета вручную'"
    :style="{ width: '90vw', maxWidth: '1200px' }"
    :breakpoints="{ '960px': '75vw', '640px': '90vw' }"
    class="manual-invoice-form"
  >
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Левая колонка: Данные клиента и организации -->
      <div class="space-y-6">
        <!-- Данные клиента -->
        <Card>
          <template #title>
            <div class="flex items-center">
              <i class="pi pi-user mr-2 text-blue-500"></i>
              Данные клиента
            </div>
          </template>
          <template #content>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Контактное лицо *</label>
                <InputText
                  v-model="formData.client.client_name"
                  :class="['w-full', { 'p-invalid': formErrors.client.client_name }]"
                  placeholder="Иванов Иван Иванович"
                />
                <small v-if="formErrors.client.client_name" class="p-error">{{ formErrors.client.client_name }}</small>
              </div>
              
              <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-center text-blue-700">
                  <i class="pi pi-info-circle mr-2"></i>
                  <span class="text-sm font-medium">Название организации будет автоматически заполнено при выборе в разделе "Реквизиты организации"</span>
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <InputText
                    v-model="formData.client.client_mail"
                    type="email"
                    :class="['w-full', { 'p-invalid': formErrors.client.client_mail }]"
                    placeholder="<EMAIL>"
                  />
                  <small v-if="formErrors.client.client_mail" class="p-error">{{ formErrors.client.client_mail }}</small>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Телефон</label>
                  <InputText
                    v-model="formData.client.client_phone"
                    :class="['w-full', { 'p-invalid': formErrors.client.client_phone }]"
                    placeholder="+7 (999) 123-45-67"
                  />
                  <small v-if="formErrors.client.client_phone" class="p-error">{{ formErrors.client.client_phone }}</small>
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Страна</label>
                  <InputText
                    v-model="formData.client.client_country"
                    class="w-full"
                    placeholder="Россия"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Город</label>
                  <InputText
                    v-model="formData.client.client_city"
                    class="w-full"
                    placeholder="Москва"
                  />
                </div>
              </div>
              
              <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Улица</label>
                  <InputText
                    v-model="formData.client.client_street"
                    class="w-full"
                    placeholder="ул. Примерная"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Дом</label>
                  <InputText
                    v-model="formData.client.client_house"
                    class="w-full"
                    placeholder="д. 1"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Индекс</label>
                  <InputText
                    v-model="formData.client.client_postindex"
                    class="w-full"
                    placeholder="123456"
                  />
                </div>
              </div>
            </div>
          </template>
        </Card>
        
        <!-- Поиск и данные организации -->
        <Card>
          <template #title>
            <div class="flex items-center">
              <i class="pi pi-building mr-2 text-green-500"></i>
              Реквизиты организации
            </div>
          </template>
          <template #content>
            <div class="space-y-4">
              <!-- Поиск организации -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Поиск по ИНН или названию</label>
                <div class="flex gap-2">
                  <InputText
                    v-model="orgSearchQuery"
                    class="flex-1"
                    placeholder="Введите ИНН или название организации"
                    @keyup.enter="searchOrganizationHandler"
                  />
                  <Button
                    @click="searchOrganizationHandler"
                    :loading="isSearchingOrg"
                    icon="pi pi-search"
                    severity="secondary"
                  />
                </div>
              </div>
              
              <!-- Результаты поиска -->
              <div v-if="orgSearchResults.length > 0" class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Результаты поиска:</label>
                <div class="max-h-32 overflow-y-auto space-y-1">
                  <div
                    v-for="org in orgSearchResults"
                    :key="org.org_inn"
                    @click="selectOrganization(org)"
                    class="p-2 border rounded cursor-pointer hover:bg-gray-50 text-sm"
                  >
                    <div class="font-medium">{{ org.org_name }}</div>
                    <div class="text-gray-600">ИНН: {{ org.org_inn }}</div>
                  </div>
                </div>
              </div>
              
              <!-- Поля организации -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Название *</label>
                  <InputText
                    v-model="formData.org.org_name"
                    :class="['w-full', { 'p-invalid': formErrors.org.org_name }]"
                    placeholder="ООО 'Название'"
                  />
                  <small v-if="formErrors.org.org_name" class="p-error">{{ formErrors.org.org_name }}</small>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">ИНН *</label>
                  <InputText
                    v-model="formData.org.org_inn"
                    :class="['w-full', { 'p-invalid': formErrors.org.org_inn }]"
                    placeholder="1234567890"
                  />
                  <small v-if="formErrors.org.org_inn" class="p-error">{{ formErrors.org.org_inn }}</small>
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">КПП</label>
                  <InputText
                    v-model="formData.org.org_kpp"
                    class="w-full"
                    placeholder="123456789"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">БИК</label>
                  <InputText
                    v-model="formData.org.org_bik"
                    :class="['w-full', { 'p-invalid': formErrors.org.org_bik }]"
                    placeholder="044525225"
                  />
                  <small v-if="formErrors.org.org_bik" class="p-error">{{ formErrors.org.org_bik }}</small>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Адрес</label>
                <Textarea
                  v-model="formData.org.org_adress"
                  class="w-full"
                  :rows="2"
                  placeholder="Полный адрес организации"
                />
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Расчетный счет</label>
                  <InputText
                    v-model="formData.org.org_rschet"
                    :class="['w-full', { 'p-invalid': formErrors.org.org_rschet }]"
                    placeholder="40702810000000000000"
                  />
                  <small v-if="formErrors.org.org_rschet" class="p-error">{{ formErrors.org.org_rschet }}</small>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Корр. счет</label>
                  <InputText
                    v-model="formData.org.org_kschet"
                    :class="['w-full', { 'p-invalid': formErrors.org.org_kschet }]"
                    placeholder="30101810000000000000"
                  />
                  <small v-if="formErrors.org.org_kschet" class="p-error">{{ formErrors.org.org_kschet }}</small>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Банк</label>
                <InputText
                  v-model="formData.org.org_bank"
                  class="w-full"
                  placeholder="Название банка"
                />
              </div>
            </div>
          </template>
        </Card>
      </div>
      
      <!-- Правая колонка: Товары и доставка -->
      <div class="space-y-6">
        <!-- Товары -->
        <Card>
          <template #title>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <i class="pi pi-box mr-2 text-orange-500"></i>
                Товары
              </div>
              <Button
                @click="showProductSearch = true"
                size="small"
                icon="pi pi-plus"
                label="Добавить товар"
              />
            </div>
          </template>
          <template #content>
            <div class="space-y-3">
              <div
                v-for="(item, index) in formData.productData"
                :key="index"
                class="border rounded-lg p-3 bg-gray-50"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1 space-y-2">
                    <div class="font-medium text-sm">{{ item.product.prod_analogsku }}</div>
                    <div class="text-xs text-gray-600">{{ item.product.prod_purpose }}</div>
                    <div class="text-xs text-gray-500">
                      {{ item.product.prod_manuf }} | {{ item.product.prod_size }}
                    </div>
                    
                    <div class="flex items-center gap-4">
                      <div class="flex items-center gap-2">
                        <label class="text-xs text-gray-600">Кол-во:</label>
                        <InputNumber
                          v-model="item.qty"
                          :min="1"
                          :max="999"
                          class="w-20"
                          size="small"
                        />
                      </div>
                      
                      <div class="text-xs text-gray-600">
                        Цена: {{ formatPrice(item.product.prod_price) }} ₽
                      </div>
                      
                      <div class="text-xs font-medium">
                        Сумма: {{ formatPrice(item.product.prod_price * item.qty) }} ₽
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    @click="removeProduct(index)"
                    size="small"
                    severity="danger"
                    text
                    icon="pi pi-trash"
                  />
                </div>
              </div>
              
              <div v-if="formData.productData.length === 0" class="text-center text-gray-500 py-4">
                Товары не добавлены
                <small v-if="formErrors.products" class="p-error block mt-2">{{ formErrors.products }}</small>
              </div>
              
              <div v-if="formData.productData.length > 0" class="border-t pt-3">
                <div class="flex justify-between items-center font-medium">
                  <span>Общая сумма:</span>
                  <span class="text-lg">{{ formatPrice(totalAmount) }} ₽</span>
                </div>
              </div>
            </div>
          </template>
        </Card>
        
        <!-- Доставка -->
        <Card>
          <template #title>
            <div class="flex items-center">
              <i class="pi pi-truck mr-2 text-purple-500"></i>
              Доставка
            </div>
          </template>
          <template #content>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Тип доставки</label>
                <Dropdown
                  v-model="formData.shippingType"
                  :options="shippingOptions"
                  optionLabel="label"
                  optionValue="value"
                  class="w-full"
                  placeholder="Выберите тип доставки"
                />
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Код страны</label>
                  <InputNumber
                    v-model="formData.countryId"
                    class="w-full"
                    placeholder="643"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Почтовый индекс</label>
                  <InputNumber
                    v-model="formData.shippingIndex"
                    class="w-full"
                    placeholder="123456"
                  />
                </div>
              </div>
            </div>
          </template>
        </Card>
      </div>
    </div>
    
    <!-- Настройки счета -->
    <Card class="mt-6">
      <template #title>
        <div class="flex items-center">
          <i class="pi pi-file-edit mr-2 text-indigo-500"></i>
          Настройки счета
        </div>
      </template>
      <template #content>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Номер счета</label>
            <InputText
              v-model="invoiceSettings.number"
              class="w-full"
              placeholder="Автоматически"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Дата счета *</label>
            <InputText
              v-model="invoiceSettings.date"
              type="date"
              :class="['w-full', { 'p-invalid': formErrors.invoice.date }]"
            />
            <small v-if="formErrors.invoice.date" class="p-error">{{ formErrors.invoice.date }}</small>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Валюта</label>
            <Dropdown
              v-model="invoiceSettings.currency"
              :options="currencyOptions"
              optionLabel="label"
              optionValue="value"
              class="w-full"
              placeholder="Выберите валюту"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Срок оплаты (дней) *</label>
            <InputNumber
              v-model="invoiceSettings.paymentTerms"
              :min="1"
              :max="365"
              :class="['w-full', { 'p-invalid': formErrors.invoice.paymentTerms }]"
              placeholder="14"
            />
            <small v-if="formErrors.invoice.paymentTerms" class="p-error">{{ formErrors.invoice.paymentTerms }}</small>
          </div>
        </div>
        
        <div class="mt-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">Комментарий к счету</label>
          <Textarea
            v-model="invoiceSettings.comment"
            class="w-full"
            :rows="2"
            placeholder="Дополнительная информация для счета"
          />
        </div>
      </template>
    </Card>
    
    <!-- Поиск товаров -->
    <Dialog
      v-model:visible="showProductSearch"
      modal
      header="Поиск товаров"
      :style="{ width: '80vw', maxWidth: '800px' }"
    >
      <ProductSearchComponent @select="addProduct" />
    </Dialog>
    
    <!-- Кнопки действий -->
    <template #footer>
      <div class="flex justify-between">
        <Button
          @click="resetForm"
          label="Сбросить"
          severity="secondary"
          outlined
        />
        
        <div class="flex gap-2">
          <Button
            @click="visible = false"
            label="Отмена"
            severity="secondary"
          />
          
          <Button
            @click="generateInvoice"
            :loading="isGenerating"
            label="Создать счет"
            icon="pi pi-file-pdf"
          />
        </div>
      </div>
    </template>
  </Dialog>
  
  <!-- EmailSender компонент -->
  <EmailSender
    v-model:visible="showEmailSender"
    :recipient-email="emailData.recipientEmail"
    :message-content="emailData.messageContent"
    :session-id="props.sessionId"
    @email-sent="onEmailSent"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import Dialog from 'primevue/dialog'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import InputNumber from 'primevue/inputnumber'
import Button from 'primevue/button'
import Dropdown from 'primevue/dropdown'
import { extractDataFromMessages, type ExtractedChatData } from '@/utils/chatDataExtractor'
import { searchOrganization, formatSearchQuery, type OrganizationData } from '@/utils/organizationSearch'
import type { Message, GenerateDocToolInput } from '@/lib/interfaces/Agent'
import { useAgentStore } from '@/stores/agent'
import ProductSearchComponent from './ProductSearchComponent.vue'
import EmailSender from './EmailSender.vue'

// Утилиты валидации
const validateINN = (inn: string): boolean => {
  if (!inn) return false
  const cleanINN = inn.replace(/\D/g, '')
  return cleanINN.length === 10 || cleanINN.length === 12
}

const validateEmail = (email: string): boolean => {
  if (!email) return true // Email не обязательный
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const validatePhone = (phone: string): boolean => {
  if (!phone) return true // Телефон не обязательный
  const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/
  return phoneRegex.test(phone)
}

const validateBIK = (bik: string): boolean => {
  if (!bik) return false
  const cleanBIK = bik.replace(/\D/g, '')
  return cleanBIK.length === 9
}

const validateAccount = (account: string): boolean => {
  if (!account) return false
  const cleanAccount = account.replace(/\D/g, '')
  return cleanAccount.length === 20
}

interface Props {
  visible: boolean
  messages?: Message[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'invoice-generated', data: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const toast = useToast()
const agentStore = useAgentStore()

// Локальное состояние видимости
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// Состояние формы
const formData = ref<GenerateDocToolInput>({
  countryId: 643,
  shippingIndex: undefined,
  shippingType: 'standard',
  productData: [],
  client: {
    fullorgname: '',
    client_name: '',
    client_mail: '',
    client_phone: '',
    client_country: 'Россия',
    client_city: '',
    client_street: '',
    client_house: '',
    client_postindex: ''
  },
  org: {
    org_name: '',
    org_adress: '',
    org_inn: '',
    org_kpp: '',
    org_rschet: '',
    org_kschet: '',
    org_bik: '',
    org_bank: ''
  }
})

// Состояние поиска организации
const orgSearchQuery = ref('')
const orgSearchResults = ref<OrganizationData[]>([])
const isSearchingOrg = ref(false)

// Состояние поиска товаров
const showProductSearch = ref(false)

// Состояние генерации
const isGenerating = ref(false)

// Состояние EmailSender
const showEmailSender = ref(false)
const emailData = ref({
  recipientEmail: '',
  messageContent: '',
  invoiceFiles: null as any
})

// Настройки счета
const invoiceSettings = ref({
  number: '',
  date: new Date().toISOString().split('T')[0],
  currency: 'RUB',
  paymentTerms: 14,
  comment: ''
})

// Опции валют
const currencyOptions = [
  { label: 'Российский рубль (₽)', value: 'RUB' },
]

// Состояние ошибок
const formErrors = ref({
  org: {
    org_name: '',
    org_inn: '',
    org_bik: '',
    org_rschet: '',
    org_kschet: ''
  },
  client: {
    client_name: '',
    client_inn: '',
    client_mail: '',
    client_phone: ''
  },
  products: '',
  invoice: {
    date: '',
    paymentTerms: ''
  }
})

// Опции доставки
const shippingOptions = [
  { label: 'Почта РФ', value: 'standard' },
  { label: 'Курьер', value: 'express' }
]

// Вычисляемые свойства
const totalAmount = computed(() => {
  return formData.value.productData.reduce((sum, item) => {
    return sum + (item.product.prod_price * item.qty)
  }, 0)
})

// Методы
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('ru-RU', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(price)
}

const searchOrganizationHandler = async () => {
  if (!orgSearchQuery.value.trim()) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Введите ИНН или название организации для поиска'
    })
    return
  }
  
  isSearchingOrg.value = true
  
  try {
    const formattedQuery = formatSearchQuery(orgSearchQuery.value)
    const result = await searchOrganization(formattedQuery)
    
    if (result) {
      orgSearchResults.value = [result]
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Организация найдена'
      })
    } else {
      orgSearchResults.value = []
      toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'Организация не найдена'
      })
    }
  } catch (error) {
    console.error('Ошибка поиска организации:', error)
    orgSearchResults.value = []
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: error instanceof Error ? error.message : 'Не удалось найти организацию'
    })
  } finally {
    isSearchingOrg.value = false
  }
}

const selectOrganization = (org: OrganizationData) => {
  formData.value.org = { ...org }
  
  // Заполняем данные клиента на основе выбранной организации
  formData.value.client.companyName = org.name || ''
  formData.value.client.contactPerson = org.contactPerson || ''
  formData.value.client.email = org.email || ''
  formData.value.client.phone = org.phone || ''
  formData.value.client.address = org.address || ''
  
  orgSearchResults.value = []
  orgSearchQuery.value = ''
  
  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Реквизиты организации и данные клиента загружены'
  })
}

const addProduct = (product: any) => {
  const newItem = {
    id: product.prod_id,
    qty: 1,
    product: {
      prod_sku: product.prod_sku,
      prod_analogsku: product.prod_analogsku,
      prod_price: product.prod_price,
      prod_manuf: product.prod_manuf,
      prod_type: product.prod_type,
      prod_size: product.prod_size,
      prod_purpose: product.prod_purpose
    }
  }
  
  formData.value.productData.push(newItem)
  showProductSearch.value = false
  
  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Товар добавлен в счет'
  })
}

const removeProduct = (index: number) => {
  formData.value.productData.splice(index, 1)
  
  toast.add({
    severity: 'info',
    summary: 'Информация',
    detail: 'Товар удален из счета'
  })
}

const resetForm = () => {
  formData.value = {
    countryId: 643,
    shippingIndex: undefined,
    shippingType: 'standard',
    productData: [],
    client: {
      fullorgname: '',
      client_name: '',
      client_mail: '',
      client_phone: '',
      client_country: 'Россия',
      client_city: '',
      client_street: '',
      client_house: '',
      client_postindex: ''
    },
    org: {
      org_name: '',
      org_adress: '',
      org_inn: '',
      org_kpp: '',
      org_rschet: '',
      org_kschet: '',
      org_bik: '',
      org_bank: ''
    }
  }
  
  // Сброс ошибок
  formErrors.value = {
    org: {
      org_name: '',
      org_inn: '',
      org_bik: '',
      org_rschet: '',
      org_kschet: ''
    },
    client: {
      client_name: '',
      client_inn: '',
      client_mail: '',
      client_phone: ''
    },
    products: '',
    invoice: {
      date: '',
      paymentTerms: ''
    }
  }
  
  orgSearchQuery.value = ''
  orgSearchResults.value = []
  
  toast.add({
    severity: 'info',
    summary: 'Информация',
    detail: 'Форма очищена'
  })
}

// Функция генерации шаблона email
const generateEmailTemplate = (files: any): string => {
  const currentDate = new Date().toLocaleDateString('ru-RU')
  const clientName = formData.value.client.client_name || 'Уважаемый клиент'
  const orgName = formData.value.org.org_name || 'Мир Сальников'
  
  let template = `<p>Направляем Вам счет</p><br>`
  
  if (files) {
    template += `<p><strong>Ссылки для скачивания:</strong></p>`
    template += `<ul>`
    
    if (files.xlsx?.url && files.xlsx.url !== '#') {
      template += `<li><a href="${files.xlsx.url}" target="_blank">Скачать счет в формате XLSX</a></li>`
    }
    
    if (files.pdf?.url && files.pdf.url !== '#') {
      template += `<li><a href="${files.pdf.url}" target="_blank">Скачать счет в формате PDF</a></li>`
    }
    
    template += `</ul><br>`
  }
  
  // template += `<p>Просим произвести оплату в течение ${invoiceSettings.value.paymentTerms} дней с момента получения счета.</p><br>`
  template += `<p>При возникновении вопросов, пожалуйста, свяжитесь с нами.</p><br>`

  return template
}

// Обработчик отправки email
const onEmailSent = () => {
  showEmailSender.value = false
  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Email отправлен клиенту'
  })
}

// Функция валидации формы
const validateForm = (): boolean => {
  // Сброс ошибок
  formErrors.value = {
    org: {
      org_name: '',
      org_inn: '',
      org_bik: '',
      org_rschet: '',
      org_kschet: ''
    },
    client: {
      client_name: '',
      client_inn: '',
      client_mail: '',
      client_phone: ''
    },
    products: '',
    invoice: {
      date: '',
      paymentTerms: ''
    }
  }
  
  let isValid = true
  
  // // Валидация организации
  // if (!formData.value.org.org_name.trim()) {
  //   formErrors.value.org.org_name = 'Название организации обязательно'
  //   isValid = false
  // }
  
  // if (!validateINN(formData.value.org.org_inn)) {
  //   formErrors.value.org.org_inn = 'Некорректный ИНН (должен содержать 10 или 12 цифр)'
  //   isValid = false
  // }
  
  // if (formData.value.org.org_bik && !validateBIK(formData.value.org.org_bik)) {
  //   formErrors.value.org.org_bik = 'Некорректный БИК (должен содержать 9 цифр)'
  //   isValid = false
  // }
  
  // if (formData.value.org.org_rschet && !validateAccount(formData.value.org.org_rschet)) {
  //   formErrors.value.org.org_rschet = 'Некорректный расчетный счет (должен содержать 20 цифр)'
  //   isValid = false
  // }
  
  // if (formData.value.org.org_kschet && !validateAccount(formData.value.org.org_kschet)) {
  //   formErrors.value.org.org_kschet = 'Некорректный корреспондентский счет (должен содержать 20 цифр)'
  //   isValid = false
  // }
  
  // // Валидация клиента
  // if (!formData.value.client.client_name.trim()) {
  //   formErrors.value.client.client_name = 'Название клиента обязательно'
  //   isValid = false
  // }
  
  // if (formData.value.client.client_mail && !validateEmail(formData.value.client.client_mail)) {
  //   formErrors.value.client.client_mail = 'Некорректный email'
  //   isValid = false
  // }
  
  // if (formData.value.client.client_phone && !validatePhone(formData.value.client.client_phone)) {
  //   formErrors.value.client.client_phone = 'Некорректный номер телефона'
  //   isValid = false
  // }
  
  // // Валидация товаров
  // if (formData.value.productData.length === 0) {
  //   formErrors.value.products = 'Добавьте хотя бы один товар в счет'
  //   isValid = false
  // }
  
  return isValid
}

const generateInvoice = async () => {
  // Валидация формы
  if (!validateForm()) {
    toast.add({
      severity: 'warn',
      summary: 'Ошибки валидации',
      detail: 'Исправьте ошибки в форме перед созданием счета'
    })
    return
  }
  
  isGenerating.value = true
  
  try {
    // Подготавливаем данные для API
    const apiPayload = {
      countryId: formData.value.countryId || 643,
      shippingIndex: formData.value.shippingIndex || 101000,
      shippingType: formData.value.shippingType || 'standard',
      productData: formData.value.productData,
      client: formData.value.client,
      org: formData.value.org
    }

    // Получаем URL API из переменных окружения
    const apiURL = 'https://api.mirsalnikov.ru/service/documents/offer/' //import.meta.env.VITE_DOC_API_URL || process.env.DOC_API_URL
    
    if (!apiURL) {
      throw new Error('DOC_API_URL не настроен в переменных окружения')
    }

    // Формируем запрос к API
    const requestBody = {
      ...apiPayload,
      filename: "99_demo_schet_ip_igolkin.xlsx",
      fileResponse: true,
    }

    console.log('🚀 Отправка запроса на создание документа:', requestBody)

    const response = await fetch(apiURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(60000) // 60 секунд timeout
    })

    if (!response.ok) {
      throw new Error(`Ошибка сервера: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    console.log('📄 Результат создания документа:', result)

    // Проверяем успешность создания
    if (!result.success) {
      throw new Error(result.message || 'Не удалось создать документ')
    }

    // Проверяем наличие файлов
    if (!result.files?.xlsx?.url || result.files.xlsx.url === '#') {
      throw new Error('Документ не был создан на сервере')
    }

    // Эмитим событие с данными счета и ссылками на файлы
    emit('invoice-generated', { 
      ...formData.value, 
      settings: invoiceSettings.value,
      files: result.files
    })
    
    // toast.add({
    //   severity: 'success',
    //   summary: 'Успешно',
    //   detail: `Счет создан успешно. Файл: ${result.files.xlsx.fileName}`
    // })

    // Открываем файл в новой вкладке
    // if (result.files.xlsx.url && result.files.xlsx.url !== '#') {
    //   window.open(result.files.xlsx.url, '_blank')
    // }

    // Подготавливаем данные для EmailSender
    emailData.value = {
      recipientEmail: formData.value.client.client_mail || '',
      messageContent: generateEmailTemplate(result.files),
      invoiceFiles: result.files
    }

    // Закрываем форму создания счета и открываем EmailSender
    visible.value = false
    showEmailSender.value = true
    
  } catch (error) {
    console.error('Ошибка создания счета:', error)
    
    let errorMessage = 'Не удалось создать счет'
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        errorMessage = 'Превышено время ожидания создания документа'
      } else if (error.message.includes('DOC_API_URL')) {
        errorMessage = 'Сервис создания документов не настроен'
      } else {
        errorMessage = error.message
      }
    }
    
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: errorMessage
    })
  } finally {
    isGenerating.value = false
  }
}

// Предзаполнение формы из контекста чата
const prefillFromMessages = () => {
  if (!props.messages || props.messages.length === 0) return
  
  const extractedData = extractDataFromMessages(props.messages)
  
  if (extractedData.client) {
    formData.value.client = { ...formData.value.client, ...extractedData.client }
  }
  
  if (extractedData.org) {
    formData.value.org = { ...formData.value.org, ...extractedData.org }
  }
  
  if (extractedData.products && extractedData.products.length > 0) {
    formData.value.productData = [...extractedData.products]
  }
  
  if (extractedData.shippingData) {
    if (extractedData.shippingData.countryId) {
      formData.value.countryId = extractedData.shippingData.countryId
    }
    if (extractedData.shippingData.shippingIndex) {
      formData.value.shippingIndex = extractedData.shippingData.shippingIndex
    }
    if (extractedData.shippingData.shippingType) {
      formData.value.shippingType = extractedData.shippingData.shippingType
    }
  }
}

// Наблюдатели
watch(() => props.visible, (newValue) => {
  if (newValue) {
    prefillFromMessages()
  }
})

watch(() => props.messages, () => {
  if (props.visible) {
    prefillFromMessages()
  }
}, { deep: true })

// Инициализация
onMounted(() => {
  if (props.visible) {
    prefillFromMessages()
  }
})
</script>

<style scoped>
.manual-invoice-form :deep(.p-dialog-content) {
  padding: 1.5rem;
}

.manual-invoice-form :deep(.p-card-content) {
  padding: 1rem;
}

.manual-invoice-form :deep(.p-card-title) {
  margin-bottom: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
}
</style>