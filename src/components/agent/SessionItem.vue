<template>
  <div :class="[
      'group relative p-3 rounded-lg cursor-pointer transition-all duration-200 border',
      isActive
        ? 'bg-blue-50 border-blue-200 shadow-sm'
        : 'hover:bg-gray-100 border-transparent hover:border-gray-200'
    ]" @click="$emit('select')">
    <div class="flex justify-between items-start">
      <div class="flex-1 min-w-0 pr-2">
        <!-- Заголовок сессии -->
        <h2 :class="[
          'font-medium truncate font-semibold',
          isActive ? 'text-blue-900' : 'text-gray-900'
        ]">
          {{ session.id }}
        </h2>
        <h3 :class="[
            'font-medium truncate text-sm',
            isActive ? 'text-blue-900' : 'text-gray-900'
          ]">
          {{ session.title }}
        </h3>

        <!-- Последнее сообщение -->
        <p v-if="session.lastMessage" :class="[
            'text-xs truncate mt-1',
            isActive ? 'text-blue-700' : 'text-gray-500'
          ]">
          {{ session.lastMessage }}
        </p>

        <!-- Метаинформация -->
        <div class="flex items-center justify-between mt-2">
          <div class="flex items-center space-x-2 text-xs">
            <!-- Дата обновления -->
            <span :class="[
                'flex items-center',
                isActive ? 'text-blue-600' : 'text-gray-400'
              ]">
              <i class="pi pi-clock mr-1"></i>
              {{ formatDate(new Date(session.updatedAt)) }}
            </span>

            <!-- Количество сообщений -->
            <span :class="[
                'flex items-center',
                isActive ? 'text-blue-600' : 'text-gray-400'
              ]">
              <i class="pi pi-comments mr-1"></i>
              {{ session.messageCount }}
            </span>
          </div>

          <!-- Индикатор клиентской информации -->
          <div v-if="session.metadata?.clientInfo" class="flex items-center">
            <i :class="[
                'pi pi-building text-xs',
                isActive ? 'text-blue-500' : 'text-gray-400'
              ]" v-tooltip.top="`Организация: ${session.metadata.clientInfo.orgName || 'Неизвестно'}`"></i>
          </div>
        </div>
      </div>

      <!-- Кнопка удаления -->
      <button @click.stop="handleDelete" :class="[
          'opacity-0 group-hover:opacity-100 ml-2 p-1 rounded transition-all duration-200',
          'hover:bg-red-100 text-gray-400 hover:text-red-500',
          'focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1'
        ]" v-tooltip.top="'Удалить чат'">
        <i class="pi pi-trash text-xs"></i>
      </button>
    </div>

    <!-- Индикатор активности -->
    <div v-if="isActive" class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-blue-500 rounded-r"></div>
  </div>
</template>

<script setup lang="ts">
import type { ChatSession } from '@/lib/interfaces/Agent'

interface Props {
  session: ChatSession
  isActive: boolean
}

interface Emits {
  (e: 'select'): void
  (e: 'delete'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const handleDelete = () => {
  // Можно добавить подтверждение удаления
  if (confirm('Вы уверены, что хотите удалить этот чат?')) {
    emit('delete')
  }
}

const formatDate = (date: Date): string => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMinutes < 1) return 'Только что'
  if (diffMinutes < 60) return `${diffMinutes} мин.`
  if (diffHours < 24) return `${diffHours} ч.`
  if (diffDays === 1) return 'Вчера'
  if (diffDays < 7) return `${diffDays} дн.`
  
  return date.toLocaleDateString('ru-RU', {
    day: '2-digit',
    month: '2-digit'
  })
}
</script>

<style scoped>
/* Плавная анимация для кнопки удаления */
.group:hover .opacity-0 {
  transition-delay: 0.1s;
}

/* Стили для индикатора активности */
.absolute.left-0 {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(-50%) scaleY(0);
  }
  to {
    transform: translateY(-50%) scaleY(1);
  }
}
</style>
