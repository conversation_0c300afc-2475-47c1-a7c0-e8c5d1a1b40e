<template>
  <Dialog
    v-model:visible="visible"
    header="Отправить сообщение по email"
    class="w-auto flex"
    modal
  >
    <div class="space-y-4">
      <!-- Email получателя -->
      <div>
        <label class="block text-sm font-medium mb-1">Email получателя *</label>
        <InputText
          v-model="emailData.to"
          placeholder="<EMAIL>"
          class="w-full"
          :class="{ 'p-invalid': !emailData.to }"
        />
        <small v-if="!emailData.to" class="p-error">Укажите email получателя</small>
      </div>

      <!-- Тема письма -->
      <div>
        <label class="block text-sm font-medium mb-1">Тема письма *</label>
        <InputText
          v-model="emailData.subject"
          placeholder="Ответ на ваш запрос"
          class="w-full"
          :class="{ 'p-invalid': !emailData.subject }"
        />
        <small v-if="!emailData.subject" class="p-error">Укажите тему письма</small>
      </div>

      <!-- Содержимое письма -->
      <div>
        <label class="block text-sm font-medium mb-1">Содержимое письма</label>
        <Editor
          v-model="emailData.message"
          editorStyle="height: 300px"
          :modules="editorModules"
        />
      </div>

      <!-- Предварительный просмотр -->
      <div v-if="emailData.message" class="border rounded-lg p-3 bg-gray-50">
        <div class="text-sm font-medium mb-2">Предварительный просмотр:</div>
        <div class="prose prose-sm max-w-none" v-html="emailData.message"></div>
      </div>

      <!-- Опции -->
      <!-- <div class="flex items-center gap-4">
        <div class="flex items-center">
          <Checkbox
            v-model="emailData.includeOriginalMessage"
            inputId="includeOriginal"
            binary
          />
          <label for="includeOriginal" class="ml-2 text-sm">
            Включить исходное сообщение клиента
          </label>
        </div>
      </div> -->
    </div>

    <template #footer>
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">
          <i class="pi pi-info-circle mr-1"></i>
          Письмо будет отправлено через систему email
        </div>
        <div class="flex gap-2">
          <Button
            @click="visible = false"
            label="Отмена"
            severity="secondary"
            outlined
          />
          <Button
            @click="sendEmail"
            label="Отправить"
            icon="pi pi-send"
            :loading="isSending"
            :disabled="!canSend"
          />
        </div>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useToast } from 'primevue/usetoast'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Editor from 'primevue/editor'
import Button from 'primevue/button'
import Checkbox from 'primevue/checkbox'
import { api } from '@/lib/_api'
import { convertMessageContentToHtml } from '@/utils/messageContentConverter'

interface Props {
  visible: boolean
  messageContent: string
  recipientEmail?: string
  originalMessage?: string
  sessionId?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'email-sent', data: { to: string; subject: string; message: string }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const toast = useToast()

// Локальное состояние видимости
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// Данные для отправки email
const emailData = ref({
  to: '',
  subject: '',
  message: '',
  includeOriginalMessage: false
})

const isSending = ref(false)

// Модули для редактора
const editorModules = {
  toolbar: [
    ['bold', 'italic', 'underline'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    ['link'],
    ['clean']
  ]
}

// Проверка возможности отправки
const canSend = computed(() => {
  return emailData.value.to && 
         emailData.value.subject && 
         emailData.value.message &&
         !isSending.value
})

// Инициализация данных при открытии диалога
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initializeEmailData()
  }
})

// Инициализация данных email
const initializeEmailData = () => {
  // Предзаполняем email получателя если передан
  emailData.value.to = props.recipientEmail || extractEmailFromMessage() || ''
  
  // Предзаполняем тему
  emailData.value.subject = generateSubject()
  
  // Предзаполняем содержимое
  emailData.value.message = formatMessageContent()
  
  // По умолчанию включаем исходное сообщение если оно есть
  emailData.value.includeOriginalMessage = !!props.originalMessage
}

// Извлечение email из содержимого сообщения
const extractEmailFromMessage = (): string => {
  if (!props.messageContent) return ''
  
  // Ищем email в тексте сообщения
  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
  const emails = props.messageContent.match(emailRegex)
  
  return emails ? emails[0] : ''
}

// Генерация темы письма
const generateSubject = (): string => {
  const currentDate = new Date().toLocaleDateString('ru-RU')
  return `Ответ на ваш запрос от ${currentDate}`
}

// Форматирование содержимого сообщения
const formatMessageContent = (): string => {
  let content = props.messageContent || ''

  // Конвертируем контент сообщения в чистый HTML
  content = convertMessageContentToHtml(content)

  // Добавляем приветствие
  const greeting = '<p>Добрый день!</p><br>'

  // Добавляем подпись
  const signature = '<br><br><p>С уважением,<br>Мир Сальников</p><br><a href="https://mirsalnikov.ru" target="_blank">mirsalnikov.ru</a>'

  return greeting + content + signature
}



// Отправка email
const sendEmail = async () => {
  if (!canSend.value) return

  isSending.value = true

  try {
    let finalMessage = emailData.value.message

    // Добавляем исходное сообщение если нужно
    if (emailData.value.includeOriginalMessage && props.originalMessage) {
      finalMessage += '<br><br><hr><br>'
      finalMessage += '<p><strong>Ваше сообщение:</strong></p>'
      finalMessage += '<blockquote style="border-left: 3px solid #ccc; padding-left: 10px; margin: 10px 0; color: #666;">'
      // finalMessage += props.originalMessage.replace(/\n/g, '<br>')
      finalMessage += '</blockquote>'
    }

    const response = await api('service/sendmail/', {
      method: 'POST',
      data: {
        to: emailData.value.to,
        subject: emailData.value.subject,
        message: finalMessage
      }
    })

    if (response.ok) {
      toast.add({
        severity: 'success',
        summary: 'Письмо отправлено',
        detail: `Сообщение отправлено на ${emailData.value.to}`,
        life: 3000
      })

      emit('email-sent', {
        to: emailData.value.to,
        subject: emailData.value.subject,
        message: finalMessage
      })

      visible.value = false
    } else {
      throw new Error(response.statusText || 'Ошибка отправки')
    }
  } catch (error) {
    console.error('Ошибка отправки email:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка отправки',
      detail: error instanceof Error ? error.message : 'Не удалось отправить письмо',
      life: 5000
    })
  } finally {
    isSending.value = false
  }
}

// Сброс формы при закрытии
watch(visible, (newVisible) => {
  if (!newVisible) {
    emailData.value = {
      to: '',
      subject: '',
      message: '',
      includeOriginalMessage: false
    }
  }
})
</script>

<style scoped>
.prose {
  max-height: 200px;
  overflow-y: auto;
}

:deep(.ql-editor) {
  min-height: 200px;
}

:deep(.ql-toolbar) {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
}

:deep(.ql-container) {
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
}
</style>
