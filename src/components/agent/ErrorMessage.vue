<template>
  <div class="error-message bg-red-50 border border-red-200 rounded-lg p-4 my-2">
    <div class="flex items-start space-x-3">
      <div class="flex-shrink-0">
        <i class="pi pi-exclamation-triangle text-red-500 text-lg"></i>
      </div>
      <div class="flex-1">
        <h4 class="text-red-800 font-semibold mb-2">Ошибка выполнения</h4>
        <div class="text-red-700 text-sm">
          <div v-if="isToolError" class="space-y-2">
            <p><strong>Инструмент:</strong> {{ toolName }}</p>
            <p><strong>Описание:</strong> {{ errorDescription }}</p>
            <details v-if="technicalDetails" class="mt-2">
              <summary class="cursor-pointer text-red-600 hover:text-red-800">
                Технические детали
              </summary>
              <pre class="mt-2 bg-red-100 p-2 rounded text-xs overflow-x-auto whitespace-pre-wrap">{{ technicalDetails }}</pre>
            </details>
          </div>
          <div v-else>
            <p>{{ errorMessage }}</p>
          </div>
        </div>
        <div class="mt-3 text-xs text-red-600">
          <i class="pi pi-clock mr-1"></i>
          {{ formatTime(timestamp) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  errorMessage: string
  timestamp?: Date
}

const props = defineProps<Props>()

// Парсим ошибку для извлечения информации об инструменте
const parsedError = computed(() => {
  try {
    const message = props.errorMessage
    
    // Проверяем, является ли это ошибкой инструмента
    if (message.includes('Invalid arguments for tool')) {
      const toolMatch = message.match(/Invalid arguments for tool (\w+):/)
      const toolName = toolMatch ? toolMatch[1] : 'Unknown'
      
      // Извлекаем описание ошибки
      let description = 'Неверные аргументы для инструмента'
      if (message.includes('Type validation failed')) {
        description = 'Ошибка валидации типов данных'
      }
      
      // Извлекаем технические детали
      const detailsMatch = message.match(/Error message: (.+)$/)
      const technicalDetails = detailsMatch ? detailsMatch[1] : null
      
      return {
        isToolError: true,
        toolName,
        description,
        technicalDetails
      }
    }
    
    return {
      isToolError: false,
      description: message
    }
  } catch (e) {
    return {
      isToolError: false,
      description: props.errorMessage
    }
  }
})

const isToolError = computed(() => parsedError.value.isToolError)
const toolName = computed(() => parsedError.value.toolName)
const errorDescription = computed(() => parsedError.value.description)
const technicalDetails = computed(() => parsedError.value.technicalDetails)

const formatTime = (date?: Date) => {
  if (!date) return ''
  return date.toLocaleTimeString('ru-RU', {
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.error-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

details summary {
  list-style: none;
}

details summary::-webkit-details-marker {
  display: none;
}

details summary::before {
  content: '▶';
  margin-right: 0.5rem;
  transition: transform 0.2s;
}

details[open] summary::before {
  transform: rotate(90deg);
}
</style>
