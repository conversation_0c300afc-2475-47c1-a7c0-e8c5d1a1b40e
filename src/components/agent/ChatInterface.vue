<template>
  <div class="flex flex-col h-full bg-white">
    <!-- Заголовок чата -->
    <div class="border-b border-gray-200 bg-white px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <i class="pi pi-robot text-2xl text-blue-500 mr-3"></i>
          <div>
            <h1 class="text-lg font-semibold text-gray-900">
              {{ currentSession?.title || 'Новый чат с агентом' }}
            </h1>
            <p class="text-sm text-gray-500">
              Помощник для обработки заявок от юридических лиц
            </p>
          </div>
        </div>
        
        <!-- Действия -->
        <div class="flex items-center space-x-2">
          <Button
            v-if="messages.length > 0"
            @click="showManualInvoiceForm = true"
            size="small"
            severity="info"
            outlined
            v-tooltip.bottom="'Создать счет вручную'"
          >
            <i class="pi pi-file-pdf mr-1"></i>
            Счет
          </Button>
          
          <Button
            v-if="currentSessionId"
            @click="handleClearChat"
            size="small"
            severity="secondary"
            outlined
            v-tooltip.bottom="'Очистить чат'"
          >
            <i class="pi pi-refresh"></i>
          </Button>
          
          <Button
            @click="handleNewChat"
            size="small"
            severity="success"
            v-tooltip.bottom="'Новый чат'"
          >
            <i class="pi pi-plus mr-1"></i>
            Новый чат
          </Button>
        </div>
      </div>
    </div>

    <!-- Область сообщений -->
    <div class="flex-1 overflow-hidden">
      <MessageList
        :messages="messages"
        :is-loading="sendingMessage.isLoading"
      />
    </div>

    <!-- Область ввода -->
    <div class="border-t border-gray-200 bg-white p-4">
      <MessageInput
        @send-message="handleSendMessage"
        :disabled="sendingMessage.isLoading"
      />
    </div>

    <!-- Уведомления об ошибках -->
    <div
      v-if="sendingMessage.error"
      class="bg-red-50 border-l-4 border-red-400 p-4 mx-4 mb-4"
    >
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="pi pi-exclamation-triangle text-red-400"></i>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-700">
            {{ sendingMessage.error }}
          </p>
          <button
            @click="clearErrors"
            class="mt-2 text-xs text-red-600 hover:text-red-800 underline"
          >
            Закрыть
          </button>
        </div>
      </div>
    </div>

    <!-- Форма создания счета вручную -->
    <ManualInvoiceForm
      v-model:visible="showManualInvoiceForm"
      :messages="messages"
      @invoice-generated="handleInvoiceGenerated"
    />

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useToast } from 'primevue/usetoast'
import { useAgentStore } from '@/stores/agent'
import Button from 'primevue/button'
import MessageList from './MessageList.vue'
import MessageInput from './MessageInput.vue'
import ManualInvoiceForm from './ManualInvoiceForm.vue'

const agentStore = useAgentStore()
const toast = useToast()

const {
  currentSessionId,
  currentSession,
  messages,
  sendingMessage
} = storeToRefs(agentStore)

const {
  sendMessage,
  createSession,
  clearErrors
} = agentStore

// Состояние формы создания счета
const showManualInvoiceForm = ref(false)

const handleSendMessage = async (content: string, attachments?: File[]) => {
  try {
    await sendMessage(content, attachments)
  } catch (error) {
    console.error('Ошибка отправки сообщения:', error)
    // Ошибка уже обработана в store
  }
}

const handleNewChat = async () => {
  try {
    await createSession()
  } catch (error) {
    console.error('Ошибка создания нового чата:', error)
  }
}

const handleClearChat = async () => {
  if (confirm('Вы уверены, что хотите начать новый чат? Текущая история будет сохранена.')) {
    await handleNewChat()
  }
}

const handleInvoiceGenerated = (invoiceData: any) => {
  // toast.add({
  //   severity: 'success',
  //   summary: 'Успешно',
  //   detail: 'Счет создан и отправлен в чат'
  // })
  
  // Можно добавить дополнительную логику обработки созданного счета
  console.log('Создан счет:', invoiceData)
}
</script>

<style scoped>
/* Анимация для приветственного сообщения */
.absolute.inset-0 {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Стили для уведомлений */
.border-l-4 {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
