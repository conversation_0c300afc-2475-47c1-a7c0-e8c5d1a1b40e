<template>
  <form @submit.prevent="handleSubmit" class="space-y-3">
    <!-- Отображение прикрепленных файлов -->
    <div v-if="attachments.length > 0" class="flex flex-wrap gap-2">
      <div
        v-for="(file, index) in attachments"
        :key="index"
        class="flex items-center bg-gray-100 rounded-lg px-3 py-2 text-sm border"
      >
        <i class="pi pi-paperclip mr-2 text-gray-500"></i>
        <span class="mr-2 text-gray-700">{{ file.name }}</span>
        <span class="text-xs text-gray-500 mr-2">({{ formatFileSize(file.size) }})</span>
        <button
          type="button"
          @click="removeAttachment(index)"
          class="text-red-500 hover:text-red-700 transition-colors"
          :disabled="disabled"
        >
          <i class="pi pi-times"></i>
        </button>
      </div>
    </div>

    <div class="flex items-end space-x-3">
      <!-- Поле ввода сообщения -->
      <div class="flex-1">
        <Textarea
          v-model="message"
          placeholder="Опишите ваш запрос или прикрепите файл с заявкой..."
          :rows="5"
          :autoResize="true"
          :disabled="disabled"
          class="w-full"
          @keydown.enter.exact.prevent="handleSubmit"
          @keydown.enter.shift.exact="handleNewLine"
        />
      </div>

      <!-- Кнопки действий -->
      <div class="flex flex-col space-y-2">
        <!-- Кнопка прикрепления файла -->
        <Button
          type="button"
          _click="$refs.fileInput?.click()"
          :disabled="true"
          severity="secondary"
          outlined
          size="small"
          class="p-2"
          v-tooltip.top="'В разработке'"
        >
          <i class="pi pi-paperclip"></i>
        </Button>

        <!-- Кнопка отправки -->
        <Button
          type="submit"
          :disabled="disabled || (!message.trim() && attachments.length === 0)"
          :loading="disabled"
          size="small"
          class="px-4 py-2"
        >
          <i class="pi pi-send mr-1"></i>
          Отправить
        </Button>
      </div>
    </div>

    <!-- Скрытый input для файлов -->
    <input
      ref="fileInput"
      type="file"
      multiple
      @change="handleFileSelect"
      class="hidden"
      accept=".pdf,.doc,.docx,.txt,.xlsx,.xls,.eml"
    />

    <!-- Подсказка по горячим клавишам -->
    <div class="text-xs text-gray-500 flex items-center justify-between">
      <span>
        <kbd class="px-1 py-0.5 bg-gray-100 rounded text-xs">Enter</kbd> - отправить,
        <kbd class="px-1 py-0.5 bg-gray-100 rounded text-xs">Shift+Enter</kbd> - новая строка
      </span>
      <span v-if="attachments.length > 0">
        Файлов прикреплено: {{ attachments.length }}
      </span>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Textarea from 'primevue/textarea'
import Button from 'primevue/button'

interface Props {
  disabled: boolean
}

interface Emits {
  (e: 'send-message', message: string, attachments?: File[]): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const message = ref('')
const attachments = ref<File[]>([])
const fileInput = ref<HTMLInputElement>()

const handleSubmit = () => {
  if (message.value.trim() || attachments.value.length > 0) {
    emit('send-message', message.value.trim(), attachments.value.length > 0 ? attachments.value : undefined)
    message.value = ''
    attachments.value = []
    
    // Сбрасываем input файлов
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

const handleNewLine = () => {
  // Добавляем новую строку в текущую позицию курсора
  const textarea = document.querySelector('textarea')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const value = message.value
    message.value = value.substring(0, start) + '\n' + value.substring(end)
    
    // Восстанавливаем позицию курсора
    setTimeout(() => {
      textarea.selectionStart = textarea.selectionEnd = start + 1
    }, 0)
  }
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = Array.from(target.files || [])
  
  // Валидация файлов
  const validFiles = files.filter(file => {
    // Проверяем размер файла (максимум 10MB)
    if (file.size > 10 * 1024 * 1024) {
      console.warn(`Файл ${file.name} слишком большой (максимум 10MB)`)
      return false
    }
    
    // Проверяем тип файла
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'message/rfc822' // .eml файлы
    ]
    
    if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.eml')) {
      console.warn(`Файл ${file.name} имеет неподдерживаемый тип`)
      return false
    }
    
    return true
  })
  
  attachments.value.push(...validFiles)
}

const removeAttachment = (index: number) => {
  attachments.value.splice(index, 1)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
/* Стили для клавиш */
kbd {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

/* Анимация для кнопки отправки */
.p-button:not(:disabled):hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}
</style>
