<script lang="ts" setup>
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import ScrollPanel from 'primevue/scrollpanel'

const { isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryKey: ['topsalesproducts'],
  queryFn: async () => await loadData(),
  refetchOnWindowFocus: false,
  refetchOnMount: true
})

async function loadData() {
  const res = await api('/cpan/statistics/topproducts/')
  return res.body
}
</script>

<template>
  <span class="text-slate-600 dark:text-zinc-100 font-semibold">Топ склада</span>

  <ScrollPanel style="height: 320px" class="custom mt-3">
    <ul class="space-y-3">
      <li :key="item.id" v-for="(item, index) in data">
        <a class="hoverunderline" :href="'/goods?searchvalue=' + item.oem" target="_blank">
          <span class="font-semibold">{{ index + 1 }}.</span>
          <span>
            <span class="hover:underline text-sky-500 font-bold">{{ item.oem }}</span> / {{ item.code }}</span
          >
          <div>
            Средняя потребность в месяц: <span class="font-semibold">{{ item.avg_sales }}</span> шт.
          </div>
          <div>
            Наличие: <span class="font-semibold">{{ item.stock }}</span> шт.
          </div>
          <div>
            Запас : <span class="font-semibold bg-yellow-100 dark:bg-orange-700 p-1 px-2 rounded-lg">{{ item.reserve }}</span> мес.
          </div>
        </a>
      </li>
    </ul>
  </ScrollPanel>
</template>
