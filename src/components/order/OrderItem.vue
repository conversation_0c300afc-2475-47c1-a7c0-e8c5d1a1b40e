<script setup>
import InputNumber from 'primevue/inputnumber'

const props = defineProps({
  product: {
    type: Object,
    required: true
  },
  orderItemChangeHandler: {
    type: Function,
    required: true
  }
})
</script>

<template>
  <!-- <InputNumber v-model="product.orderCount" :min="0" :max="product.product_count" :prefix="`${product.prod_price} x `" @input="orderItemChangeHandler($event, product.orderCount, product)">
    <template #decrementButton>
      <i class="pi pi-minus" />
    </template>

    <template #incrementButton>
      <i class="pi pi-plus" />
    </template>
  </InputNumber> -->
  <InputNumber
    inputId="vertical"
    v-model="product.orderCount"
    mode="decimal"
    _prefix="String(product.prod_price) + ' x '"
    :unstyled="true"
    :allowEmpty="false"
    showButtons
    buttonLayout="horizontal"
    class="flex items-stretch h-8"
    inputClass="rounded-l bg-slate-200 p-1 px-2 font-bold focus:outline-transparent border-none appreance-npne w-14"
    :min="0"
    @input="(e) => orderItemChangeHandler(e.originalEvent, e.value, product)"
    :max="product.product_count"
    decrementButtonClass="bg-slate-600 text-white p-1 px-2 rounded-r"
    incrementButtonClass="bg-slate-600 text-white p-1 px-2"
    incrementButtonIcon="pi pi-plus"
    decrementButtonIcon="pi pi-minus"
  />
</template>
