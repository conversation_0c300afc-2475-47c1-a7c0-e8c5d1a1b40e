<script setup lang="ts">
import { api } from '@/lib/_api'
import { nextTick, onMounted, ref } from 'vue'

import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import Button from 'primevue/button'
import type { PrintForm } from '@/lib/interfaces/PrintForm'
import Dialog from 'primevue/dialog'
import { useToast } from 'primevue/usetoast'
import OrderSearchMini from '../order/OrderSearchMini.vue'

const toast = useToast()
const emit = defineEmits(['onSend'])

const groupsNames = ['морозова', 'иголкин', 'рти', 'орифжанов']

const props = defineProps({
  orderId: Number,
  senderIndex: Number
})

//console.log('🚀 ~ file: OrderDocuments.vue:19 ~ props:', props)

const docList = ref([])
const formList = ref<PrintForm[]>([])
const previewModalShow = ref(false)
const selectedFormBody = ref<PrintForm>()

const mergeOrdersIds = ref('')

const selectedFormKey = ref('')
const showSearchOrderForm = ref(false)

const sofKeys = ['cust_rti_opis', 'opis_bez_tovarov_rti', 'speclist']
// const senderIndex = ref(0)

async function sendToClient() {
  emit('onSend', { htmlString: selectedFormBody.value, subject: '#' + props.orderId })
}

async function loadDocList() {
  const res = await api('/service/documents/list')
  docList.value = res.body
}

async function loadFormList() {
  const res = await api('cpan/forms/list')
  formList.value = res.body
}

async function loadFormBody(key: string) {
  selectedFormKey.value = key

  if (sofKeys.includes(selectedFormKey.value)) {
    showSearchOrderForm.value = true
  }

  toast.add({
    summary: 'Загрузка данных формы...'
  })

  const searchParams = new URLSearchParams()

  searchParams.set('key', key)
  searchParams.set('orderId', String(props.orderId))
  searchParams.set('senderIndex', String(props.senderIndex))

  if (mergeOrdersIds.value) {
    searchParams.set('mergeOrdersIds', mergeOrdersIds.value)
  }

  const res = await api(`/cpan/forms/make?${searchParams.toString()}`)
  // const res = await api(`/cpan/forms/make?key=${key}&orderId=${props.orderId}`)

  if (res.ok) {
    selectedFormBody.value = res.body.html
  }

  previewModalShow.value = true
  toast.removeAllGroups()
}

async function printElement(elementId: string) {
  const elementToPrint = document.getElementById(elementId)
  const printContainer = document.getElementById('print-container')

  if (elementToPrint && printContainer) {
    printContainer.innerHTML = elementToPrint.innerHTML
    previewModalShow.value = false
    await nextTick()
    window.print()
    printContainer.innerHTML = ''
  } else {
    throw new Error('print error: !elementToPrint || !printContainer')
  }
}

function onSelectMergeOrders({ value }) {
  let v = value.map((i) => i.order_id).join(',')
  //console.log('🚀 ~ onSelectMergeOrders ~ v:', v)
  mergeOrdersIds.value = v

  // loadFormBody(selectedFormKey.value)
}

function openInNewTab() {
  const newWindow = window.open('', '_blank')

  if (newWindow) {
    // 1. Capture external stylesheets
    const styleSheets = Array.from(document.styleSheets)
      .map((sheet) => {
        if (sheet.href) {
          return `<link rel="stylesheet" href="${sheet.href}">`
        }
        // Capture inline stylesheet rules
        try {
          const rules = Array.from(sheet.cssRules)
            .map((rule) => rule.cssText)
            .join('\n')
          return `<style>${rules}</style>`
        } catch (e) {
          return ''
        }
      })
      .join('\n')

    // 2. Get root and body classes
    const rootClasses = document.documentElement.className
    const bodyClasses = document.body.className.replace('p-overflow-hidden', '')

    // 3. Write complete HTML
    newWindow.document.write(`
      <!DOCTYPE html>
      <html class="${rootClasses}">
        <head>
          <title>Предпросмотр документа заказ #${props.orderId}</title>
          ${styleSheets}
          
          <style>
          @media print {
            body {
              zoom: 80%; /* For older browsers */
              transform: scale(0.8); /* Modern browsers */
              transform-origin: top left;
            }
          }
          </style>
        </head>
        <body contenteditable="true" class="${bodyClasses}">
          <div class="w-full bg-white dark:bg-zinc-200">
            ${selectedFormBody.value}
          </div>
        </body>
      </html>
    `)

    newWindow.document.close()
  }
}

onMounted(() => {
  loadDocList()
  loadFormList()
})
</script>

<template>
  <div>
    <div class="card">
      <TabView class="bbg-white dark:bg-zinc-900 sshadow-xl sshadow-slate-200 dark:shadow-none rounded-md">
        <TabPanel v-for="(name, index) in groupsNames" :key="name" :header="name.charAt(0).toUpperCase() + name.slice(1)">
          <ul class="space-y-1">
            <li class="hover:dark:bg-slate-700 p-1 px-2 rounded-md" v-for="(document, index) in docList.filter((i) => i.runame.includes(name))" :key="index">
              <a class="capitalize hover:underline" :href="`https://api.mirsalnikov.ru/service/documents/prepare?orderId=${props.orderId}&documentName=` + document.name" target="_blank" rel="r">
                {{ index + 1 }}. {{ document.name.includes('RUMI') ? document.name : document.runame }}
              </a>
              <span class="bg-green-400 text-white px-1 py-1 mx-2 font-semibold text-xs rounded-lg">Excel</span>
            </li>
          </ul>
          <ul class="space-y-1">
            <li
              @click="() => loadFormBody(form.page_key)"
              class="cursor-pointer capitalize hover:underline hover:dark:bg-slate-700 p-1 px-2 rounded-md"
              v-for="(form, index) in formList.filter((i) => i.page_title.toLowerCase().includes(name))"
              :key="index"
            >
              {{ index + 1 }}. {{ form.page_title }}
            </li>
          </ul>
        </TabPanel>
        <TabPanel header="Прочие">
          <ul class="space-y-1">
            <li
              class="hover:dark:bg-slate-700 p-1 px-2 rounded-md"
              v-for="(document, index) in docList.filter((i) => !groupsNames.some((name) => i.runame.toLowerCase().includes(name.toLowerCase())))"
              :key="index"
            >
              <a class="capitalize hover:underline" :href="`https://api.mirsalnikov.ru/service/documents/prepare?orderId=${props.orderId}&documentName=` + document.name" target="_blank" rel="r">
                {{ index + 1 }}. {{ document.name.includes('RUMI') ? document.name : document.runame }}
              </a>
              <span class="bg-green-400 text-white px-1 py-1 mx-2 font-semibold text-xs rounded-lg">Excel</span>
            </li>
          </ul>
          <ul class="space-y-1">
            <li
              @click="() => loadFormBody(form.page_key)"
              class="cursor-pointer capitalize hover:underline hover:dark:bg-slate-700 p-1 px-2 rounded-md"
              v-for="(form, index) in formList.filter((form) => !groupsNames.some((name) => form.page_title.toLowerCase().includes(name.toLowerCase())))"
              :key="index"
            >
              {{ index + 1 }}. {{ form.page_title }}
            </li>
          </ul>
        </TabPanel>
        <!-- <TabPanel header="Таможня">
          <ul class="space-y-1">
            <li @click="() => loadFormBody(form.page_key)" class="cursor-pointer hover:dark:bg-slate-700 p-1 px-2 rounded-md" v-for="(form, index) in formList" :key="index">
              {{ index + 1 }}. {{ form.page_title }}
            </li>
          </ul>
        </TabPanel> -->
        <!-- <TabPanel header="Rumisota">
          <p>
            At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident,
            similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio
            cumque nihil impedit quo minus.
          </p>
        </TabPanel> -->
      </TabView>
    </div>

    <div v-if="previewModalShow">
      <Dialog v-model:visible="previewModalShow" draggable :showHeader="true" modal header="" :style="{ width: '80vw' }">
        <div class="flex justify-end">
          <!-- <OrderSearchMini :onSelect="(selected) => handleSelected(selected)" v-model:selected="selectedValues" /> -->
          <OrderSearchMini v-if="showSearchOrderForm" :onSelect="onSelectMergeOrders" />

          <Button @click="() => loadFormBody(selectedFormKey)" icon="pi pi-reload" size="small" text label="Перезагрузить форму" />
          <Button @click="() => printElement('formpreview')" icon="pi pi-print" size="small" text label="Печать" />
          <Button @click="openInNewTab" icon="pi pi-external-link" size="small" text label="Открыть в новой вкладке (тест)" />
          <Button @click="sendToClient" icon="pi pi-at" size="small" text label="Отправить клиенту" />
        </div>
        <div class="mt-2 bg-white dark:bg-zinc-200" id="formpreview" contenteditable="true" v-html="selectedFormBody"></div>
      </Dialog>
    </div>
  </div>
</template>
