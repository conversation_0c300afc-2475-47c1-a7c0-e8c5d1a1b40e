<script setup lang="ts">
import Divider from 'primevue/divider'
import Timeline from 'primevue/timeline'
import { defineProps, ref, onMounted } from 'vue'
import type { Snapshot, SnaphotBody } from '../../lib/interfaces/Order'
import { trnColumns } from '../../lib/trnColumns'
import dayjs from 'dayjs'

type PropsType = {
  data: Snapshot[]
}

type ChangeItem = {
  autorName: string
  datetime: string | Date
  targetName?: string
  key: string
  prevValue: string | number
  newValue: string | number
}

const props = defineProps({
  data: {}
})

const snapshots = ref<Snapshot[]>(props.data)

const orderCompareFields = ['order_status', 'order_shipping', 'order_payment', 'order_price', 'order_shippingprice', 'order_desc', 'order_clienttype', 'order_notice', 'order_gtd', 'order_tracknumber']
//'prod_group_count', 'prod_count',
const itemsCompareFields = [
  'prod_images',
  'prod_analogsku',
  'prod_sku',
  'prod_cat',
  'prod_morecats',
  'prod_price',
  'prod_manuf',
  'prod_year',
  'prod_model',
  'prod_type',
  'prod_uses',
  'prod_size',
  'prod_discount',
  'prod_purpose',
  'prod_material',
  'prod_weight',
  'prod_group',
  'prod_coeff',
  'prod_buy_limit',
  'buy_limit',
  '_qty',
  'discount',
  'orderCount',
  'item_count'
]
const clientCompareFields = ['client_name', 'client_mail', 'client_phone', 'client_city', 'client_country', 'client_street', 'client_house', 'client_flat', 'client_postindex']
const couponsCompareFields = ['personal']
const clientOrgCompareFields = []

const changeList = ref<ChangeItem[]>([])

function makeChanges() {
  // const firstSnapshot = snapshots.value?.[0]

  // firstSnapshot?.emailLog?.map((item) => {
  //   changeList.value.push({
  //     autorName: firstSnapshot.user,
  //     datetime: item.date,
  //     targetName: 'Исходящее сообщение: ' + item.subject,
  //     key: '',
  //     prevValue: '',
  //     newValue: item.message
  //   })
  // })

  snapshots.value.map((snapshot, index) => {
    const nextSnapshot = snapshots.value[index + 1]
    const prevSnapshot = snapshots.value[index - 1]

    if (!nextSnapshot) {
      return
    }

    orderCompareFields.map((key) => {
      if (nextSnapshot.body[key] !== snapshot.body[key]) {
        changeList.value.push({
          autorName: snapshot.user,
          datetime: snapshot.created_at || snapshot.body.order_lastupdate,
          targetName: 'Заказ',
          key,
          prevValue: nextSnapshot.body[key],
          newValue: snapshot.body[key]
        })
      }
    })

    clientCompareFields.map((key) => {
      if (nextSnapshot.body.client[key] !== snapshot.body.client[key]) {
        changeList.value.push({
          autorName: snapshot.user,
          datetime: snapshot.created_at || snapshot.body.order_lastupdate,
          targetName: 'Покупатель',
          key,
          prevValue: nextSnapshot.body.client[key],
          newValue: snapshot.body.client[key]
        })
      }
    })

    // clientOrgCompareFields.map(key => {
    //     if (nextSnapshot.body.client[key] !== snapshot.body.client[key]) {
    //         changeList.value.push({
    //             autorName: snapshot.user,
    //             datetime: snapshot.created_at || snapshot.body.order_lastupdate,
    //             targetName: 'Юр.лицо',
    //             key,
    //             prevValue: nextSnapshot.body.client[key],
    //             newValue: snapshot.body.client[key]
    //         })
    //     }
    // })

    couponsCompareFields.map((key) => {
      if (nextSnapshot.body.order_coupons[key] !== snapshot.body.order_coupons[key]) {
        changeList.value.push({
          autorName: snapshot.user,
          datetime: snapshot.created_at || snapshot.body.order_lastupdate,
          targetName: 'Скидки',
          key,
          prevValue: nextSnapshot.body.order_coupons[key],
          newValue: snapshot.body.order_coupons[key]
        })
      }
    })

    if (prevSnapshot) {
      // //console.log('🚀 ~ file: OrderSnapshotsLog.vue:124 ~ snapshots.value.map ~ prevSnapshot:', prevSnapshot)
      snapshot.body.items.map((orderItem, index) => {
        const prevSnapOrderItem = prevSnapshot.body.items.find((x) => x.prod_id === orderItem.prod_id)

        if (!prevSnapOrderItem) {
          changeList.value.push({
            autorName: snapshot.user,
            datetime: snapshot.created_at || snapshot.body.order_lastupdate,
            targetName: 'Товар ' + orderItem.prod_analogsku,
            key: '',
            prevValue: '',
            // newValue: 'Добавлен в заказ. ' + orderItem.orderCount + ' шт.'
            newValue: 'Удален из заказа'
          })
        }
      })
    }

    snapshot.body.items.map((orderItem, index) => {
      const nextSnapOrderItem = nextSnapshot.body.items.find((x) => x.prod_id === orderItem.prod_id)

      if (nextSnapOrderItem) {
        itemsCompareFields.map((key) => {
          if (orderItem[key] !== nextSnapOrderItem[key]) {
            changeList.value.push({
              autorName: snapshot.user,
              datetime: snapshot.created_at || snapshot.body.order_lastupdate,
              targetName: 'Товар ' + orderItem.prod_analogsku,
              key,
              prevValue: nextSnapOrderItem[key],
              newValue: orderItem[key]
            })
          }
        })
      } else {
        changeList.value.push({
          autorName: snapshot.user,
          datetime: snapshot.created_at || snapshot.body.order_lastupdate,
          targetName: 'Товар ' + orderItem.prod_analogsku,
          key: '',
          prevValue: '',
          newValue: 'Добавлен в заказ. ' + orderItem.orderCount + ' шт.'
        })
      }
    })

    // //console.log('compare changeList: ', changeList.value)
  })
}

onMounted(() => {
  // //console.log('snapshots:', snapshots.value)
  makeChanges()
})
</script>

<template>
  <div class="space-y-3">
    <div class="overflow-y-auto mmax-h-96">
      <Timeline class="w-auto" :value="changeList">
        <template #opposite="slotProps">
          <small class="p-text-secondary">{{ dayjs(slotProps.item.datetime).format('DD.MM.YY HH:mm') }}</small>
          <div class="p-text-secondary">{{ slotProps.item.autorName }}</div>
        </template>
        <template #content="slotProps">
          <span>
            {{ slotProps.item.targetName }}: <span class="font-semibold">{{ trnColumns(slotProps.item.key) }}</span>
          </span>
          <div>
            <span class="text-yellow-700">{{ slotProps.item.prevValue || "' '" }}</span>
            <i vv-if="slotProps.item.prevValue" class="mx-2 text-sm pi pi-arrow-right" />
            <span class="text-green-700">{{ slotProps.item.newValue || "' '" }}</span>
          </div>
        </template>
      </Timeline>
    </div>
  </div>
</template>
