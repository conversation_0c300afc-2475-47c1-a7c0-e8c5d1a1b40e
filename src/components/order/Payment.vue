<script setup lang="ts">
import { defineProps, onMounted, ref, watch } from 'vue'
import Dropdown from 'primevue/dropdown'
import Inplace from 'primevue/inplace'
import InputText from 'primevue/inputtext'

const paymentMethods = ref(['По безналичному счету', 'Банковской картой'])
const newItemValue = ref('')

const props = defineProps({
  order: {}
})

function newItemHandler(value) {
  paymentMethods.value.unshift(value)
  order.value.order_payment = value
}

const order = ref(props.order)

onMounted(() => {
  try {
    paymentMethods.value = [...new Set([...paymentMethods.value, props.order.order_payment])]
  } catch (error) {
    console.error('@@:', error)
  }
})
</script>

<template>
  <div class="flex items-center space-x-2" v-if="order">
    <Dropdown  class="drp w-44" v-model="order.order_payment" :options="paymentMethods" placeholder="Тип оплаты" />

    <Inplace :closable="true">
      <template #display>
        <i v-tooltip="'Добавить метод оплаты'" class="pi pi-plus text-lg" />
      </template>
      <template #content>
        <InputText @change="(e) => newItemHandler(e.target.value)" v-model="newItemValue" autoFocus />
      </template>
    </Inplace>
  </div>
</template>

<style>
.drp .p-dropdown .p-dropdown-label {
  @apply px-2 p-0;
}
</style>
