<script setup lang="ts">
import { defineProps, onMounted, ref, watch } from 'vue'
import Dropdown from 'primevue/dropdown'
import Inplace from 'primevue/inplace'
import InputText from 'primevue/inputtext'


const shippingMethods = ref(['Почта РФ', 'Курьер', 'СДЭК', 'Курьер КСЭ'])
const newItemValue = ref('')

const props = defineProps({
  order: {}
})

function newItemHandler(value) {
  shippingMethods.value.unshift(value)
  order.value.order_shipping = value
}

const order = ref(props.order)

onMounted(() => {
  try {
    shippingMethods.value = [...new Set([...shippingMethods.value, props.order.order_shipping])]
  } catch (error) {
    console.error('@@:', error)
  }
})
</script>

<template>
  <div class="flex items-center space-x-2" v-if="order">
    <Dropdown class="w-44" v-model="order.order_shipping" :options="shippingMethods" placeholder="Тип доставки" />

    <Inplace :closable="true">
      <template #display>
        <i v-tooltip="'Добавить метод доставки'" class="pi pi-plus text-lg" />
      </template>
      <template #content>
        <InputText @change="(e) => newItemHandler(e.target.value)" v-model="newItemValue" autoFocus />
      </template>
    </Inplace>
  </div>
</template>
