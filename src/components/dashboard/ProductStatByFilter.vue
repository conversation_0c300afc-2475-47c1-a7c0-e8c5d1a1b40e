<script setup lang="ts">
import { api } from '@/lib/_api'
import { trnColumns } from '@/lib/trnColumns'
import { useQuery } from '@tanstack/vue-query'
import AutoComplete from 'primevue/autocomplete'
import But<PERSON> from 'primevue/button'
import Calendar from 'primevue/calendar'
import InputNumber from 'primevue/inputnumber'
import MultiSelect from 'primevue/multiselect'
import { ref, watch, provide, onMounted } from 'vue'

import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'

import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { SunburstChart } from 'echarts/charts'
import { VisualMapComponent } from 'echarts/components'
import VChart, { THEME_KEY } from 'vue-echarts'
import { TreemapChart } from 'echarts/charts'
import { BarChart } from 'echarts/charts'
import SelectButton from 'primevue/selectbutton'
import { GridComponent } from 'echarts/components'
import { ToolboxComponent } from 'echarts/components'
import Tree from 'primevue/tree'

import CategorySelect from '@/components/product/CategorySelect.vue'

import { slate, sky, rose, orange, indigo, zinc, blue, red } from 'tailwindcss/colors'
import type { EChartsType } from 'echarts'

use([SunburstChart, TreemapChart, ToolboxComponent, BarChart, GridComponent, VisualMapComponent, CanvasRenderer, PieChart, TitleComponent, TooltipComponent, LegendComponent])

provide(THEME_KEY, 'light')

const chartTypes = [
  { name: 'Круговая', value: 'sunburst' },
  { name: 'Дерево', value: 'treemap' }
  // { name: 'Пирог', value: 'pie' }
  // { name: 'Бар', value: 'bar' }
]

const chartType = ref(chartTypes[0])

const legendData = ref(['A'])

watch(chartType, (value, oldValue) => {
  option.value.series.type = value.value
})

const option = ref<EChartsType>({
  color: [slate[600], sky[400], rose[500], zinc[800], blue[500], red[500], orange[400], indigo[500]],
  tooltip: {
    trigger: 'item',
    formatter: function (params) {
      // //console.log('🚀 ~ file: ProductStatByFilter.vue:32 ~ params:', params)
      const _val = Number(params.value).toLocaleString() + ' руб'
      const breadcrumbArray = params.treePathInfo?.map((i) => i.name).filter((i) => i)
      // //console.log("🚀 ~ file: ProductStatByFilter.vue:48 ~ breadcrumbArray:", breadcrumbArray)
      return breadcrumbArray?.length ? breadcrumbArray?.join(' > ') + ': ' + _val : `${params.data.name}: ${_val}`
    }
    // formatter: '{a}>{b}: {c}'
  },
  legend: {
    data: []
  },
  toolbox: {
    feature: {
      saveAsImage: {
        title: 'Сохранить как изображение', // Название опции
        type: 'png' // Формат сохраняемого изображения (может быть png, jpeg и др.)
      }
    }
  },
  // xAxis: {
  //   type: 'category'
  // },
  // yAxis: {
  //   type: 'value'
  // },
  series: {
    barWidth: 10,
    type: chartType.value.value,
    // type: 'treemap',
    emphasis: {
      focus: 'ancestor'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 20,
      bottom: 20,
      data: legendData.value
    },
    data: [],
    radius: ['15%', '90%'],
    label: {
      show: true,
      rotate: 'radial',
      position: 'inside'
      // position: 'outside',
      // position: 'inside-left',
      // formatter: function (params) {
      //   //console.log('🚀 ~ file: ProductStatByFilter.vue:52 ~ params:', params)
      //   // const breadcrumbArray = params.treePathInfo.map((i) => i.name)
      //   // return breadcrumbArray.join(' > ') + ': ' + params.value
      //   return `${params.name}: ${Number(params.value).toLocaleString()}`
      // }
    },
    width: '100%',
    height: '100%',
    roam: false, // Отключить перемещение по дереву
    breadcrumb: {
      show: true // Скрыть хлебные крошки
    }
  }
})

const filters = ref({}),
  listbox = ref<any>({}),
  currentMonth = ref(),
  currentYear = ref(new Date().getFullYear()),
  filteredItems = ref({}),
  dateRange = ref(),
  // groupby = ref(['cat_title', 'order_year', 'order_month', 'prod_sku', 'prod_analogsku', 'prod_type', 'prod_manuf', 'prod_uses', 'prod_purpose', 'prod_material', 'prod_group', 'prod_model'])
  groupbyList = ref([
    { 'label': 'Категория', 'value': 'cat_title' },
    { 'label': 'Год у заказа', 'value': 'order_year' },
    { 'label': 'Месяц у заказа', 'value': 'order_month' },
    { 'label': 'Артикл', 'value': 'prod_sku' },
    { 'label': 'ОЕМ', 'value': 'prod_analogsku' },
    { 'label': 'Тип', 'value': 'prod_type' },
    { 'label': 'Бренд', 'value': 'prod_manuf' },
    { 'label': 'Применение', 'value': 'prod_uses' },
    { 'label': 'Назначение', 'value': 'prod_purpose' },
    { 'label': 'Материал', 'value': 'prod_material' },
    { 'label': 'Группа', 'value': 'prod_group' },
    { 'label': 'Модель', 'value': 'prod_model' },
    { 'label': 'Статус заказа', 'value': 'order_status' }
  ]),
  groupby = ref(),
  resultData = ref({}),
  showChart = ref(false)

//"groupby": ["order_year","order_month"]

const { isLoading, isFetching, isError, data, error, refetch } = useQuery({
  queryKey: ['fiters'],
  queryFn: loadStatData,
  refetchOnMount: false,
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
  enabled: false //!!Object.keys(filters.value).length,
  // retry: 3,
})

const {
  isLoading: filters_isLoading,
  data: filters_data,
  error: filters_error
} = useQuery({
  queryKey: ['filtersData'],
  queryFn: loadFitersData,
  refetchOnMount: false,
  refetchOnWindowFocus: false,
  refetchOnReconnect: false
  // retry: 3,
})

watch(filters, (value, prev) => {
  //console.log('@@filters: ', filters.value)

  for (const key in filters.value) {
    if (!filters.value[key]) {
      delete filters.value[key]
    }
  }

  // //console.log('filters: ', filters.value)

  if (Object.keys(filters.value).length > 0) {
    // refetch()
  }
})

async function loadFitersData() {
  const res = await api('/service/getfiltersdata/', {
    data: {
      fields: [
        'prod_manuf',
        // 'prod_cat',
        'prod_type',
        // 'prod_year',
        // 'prod_uses',
        'prod_purpose'
        // 'prod_model'
      ]
    }
  })

  const data = res.body
  res.ok && Object.keys(data).map((key) => (data[key] = data[key].map((item) => ({ label: item, value: item }))))

  return res.ok ? data : res.message
}
async function loadStatData() {
  const res = await api('/cpan/statistics/sales/byfilter', {
    data: {
      calcGroupSum: true,
      filters: filters.value,
      currentMonth: currentMonth.value,
      currentYear: currentYear.value,
      dateRange: dateRange.value,
      groupby: groupby.value?.length ? groupby.value : undefined
    }
  })

  //console.log('response data:', res.body)

  if (res.ok) {
    resultData.value = res.body
    showChart.value = false
    showChart.value = true
  }

  return res.ok ? res.body : res.message
}

function changeFilterHandler({ value, filterName }: { value: Array<{ label: string; value: string }>; filterName: string }) {
  //console.log('changeFilterHandler e:', { value, filterName })
  let _value = value.map((i) => i.value).filter((i) => i)
  //console.log('🚀 ~ file: ProductStatByFilter.vue:233 ~ changeFilterHandler ~ _value:', _value)

  filters.value = {
    ...filters.value,
    [filterName]: _value.length > 0 ? _value : undefined
  }
}

function search(event: any, filterName: string) {
  if (!event.query.trim().length) {
    filteredItems.value[filterName] = [...filters_data.value[filterName]]
  } else {
    filteredItems.value[filterName] = filters_data.value[filterName].filter((item: any) => {
      return item.label.toLowerCase().startsWith(event.query.toLowerCase())
    })
  }
}

async function makeReport() {
  if (Object.keys(filters.value).length > 0 || groupby.value.length > 0) {
    await refetch()
  }
}

function transformDataForBar(data) {
  let result = []

  function traverse(node, depth) {
    if (!node.items) {
      result.push({
        name: node.name,
        value: node.sum,
        depth
      })
    } else {
      result.push({
        name: node.name,
        value: node.sum,
        depth
      })

      Object.keys(node.items).forEach((childName) => {
        traverse(node.items[childName], depth + 1)
      })
    }
  }

  traverse(data, 0)

  return result
}

function transformDataForEchart(input) {
  const result = []

  for (const key in input) {
    if (input.hasOwnProperty(key)) {
      const item = input[key]
      const transformedItem = {
        key: Math.random(),
        label: key,
        name: key,
        value: typeof item == 'object' ? item.sum : item
      }

      if (item.items) {
        transformedItem.children = transformDataForEchart(item.items)
      }

      result.push(transformedItem)
    }
  }

  return result
}

onMounted(() => {})

watch(resultData, () => {
  //console.log('@@resultData:', resultData.value)

  const echartData = transformDataForEchart(resultData.value)

  option.value.series.data = echartData
})
</script>

<template>
  <div>
    <div class="flex justify-between gap-10">
      <div class="w-3/5">
        <div class="font-semibold text-slate-900">Результаты</div>
        <div :class="option.series.data?.length ? 'h-[900px]' : ''">
          <SelectButton v-model="chartType" :options="chartTypes" optionLabel="name" aria-labelledby="chart type" />
          <v-chart :loading="isFetching" class="chart" :option="option" autoresize />
          <Tree v-show="option.series.data?.length" filterMode="strict" filter :loading="isFetching" :value="option.series.data">
            <template #default="slotProps">
              <b>{{ slotProps.node.name }}</b
              >: {{ Number(slotProps.node.value).toLocaleString() }} руб.
            </template></Tree
          >
        </div>
      </div>
      <div class="w-1/5 space-y-4">
        <div class="font-semibold text-slate-900">Конфигуратор</div>
        <div>
          <div>
            <div class="mt-10 font-semibold text-slate-600 dark:text-zinc-100">Разбить результат на:</div>
            <MultiSelect v-model="groupby" :options="groupbyList" optionValue="value" optionLabel="label" placeholder="Группировать результат по" class="w-full" />
          </div>
          <div class="flex justify-start gap-3">
            <div>
              <div class="mt-10 font-semibold text-slate-600 dark:text-zinc-100">Год</div>
              <InputNumber input-class="w-28" :useGrouping="false" v-model="currentYear" />
            </div>
            <div>
              <div class="mt-10 font-semibold text-slate-600 dark:text-zinc-100">Месяц</div>
              <InputNumber input-class="w-20" :useGrouping="false" v-model="currentMonth" />
            </div>
          </div>
          <div class="text-slate-500 dark:text-gray-300 font-semibold text-sm">ИЛИ</div>
          <div>
            <div class="font-semibold text-slate-600 dark:text-zinc-100">Период</div>
            <Calendar class="w-auto" showWeek showButtonBar v-model="dateRange" selectionMode="range" :manualInput="false" />
          </div>
        </div>
        <div>
          <div class="font-semibold text-slate-600 dark:text-zinc-100">Категория:</div>
          <CategorySelect multiple :onChangeHandler="(value) => changeFilterHandler({ value, filterName: 'cat_id' })" />
        </div>
        <div :key="index" class="mb-10" v-for="(filterName, index) in Object.keys(filters_data || {})">
          <div class="font-semibold text-slate-600 dark:text-zinc-100">{{ trnColumns(filterName) }}:</div>
          <div>
            <!-- <Listbox listStyle="height:250px" @change="({ value }) => changeFilterHandler({ value, filterName })" v-model="listbox[filterName]" optionLabel="label" :options="filters_data[filterName]" filter multiple /> -->
            <AutoComplete
              @change="({ value }) => changeFilterHandler({ value, filterName })"
              dropdown
              optionLabel="label"
              v-model="listbox[filterName]"
              multiple
              :suggestions="filteredItems[filterName]"
              @complete="(e: any) => search(e, filterName)"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="flex justify-end mt-2">
      <Button :loading="isFetching" @click="makeReport" label="Сформировать" />
    </div>
  </div>
</template>
