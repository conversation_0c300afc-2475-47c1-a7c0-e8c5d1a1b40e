<script lang="ts" setup>
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import dayjs from 'dayjs'
import But<PERSON> from 'primevue/button'
import Column from 'primevue/column'
import ConfirmPopup from 'primevue/confirmpopup'
import DataTable from 'primevue/datatable'
import InputSwitch from 'primevue/inputswitch'
import InputText from 'primevue/inputtext'
import Listbox from 'primevue/listbox'
import Paginator from 'primevue/paginator'
import ProgressBar from 'primevue/progressbar'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { ref } from 'vue'
import StatlistItemInfo from './StatlistItemInfo.vue'
import StatsDownload from './StatsDownload.vue'

const confirm = useConfirm()
const toast = useToast()

import('dayjs/locale/ru')

const { isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryKey: ['statList'],
  queryFn: async () => await loadData(),
  onSuccess: async (data) => onSuccessLoadData(data),
  refetchOnWindowFocus: false
})

const page = ref(1)
const limit = ref(80)
const list = ref()
const searchvalue = ref('')
const isNoStock = ref(true)
const expandedRows = ref()

const selected = ref()

async function loadData() {
  const res = await api('/cpan/statistics/list', {
    data: {
      limit: limit.value,
      searchvalue: searchvalue.value,
      isNoStock: isNoStock.value
    }
  })

  return res.body
}

async function search() {
  refetch()
}

async function onSuccessLoadData(data) {
  list.value = data
}

function deleteHandler(event, item) {
  confirm.require({
    target: event.currentTarget,
    message: 'Подтвердит удаление',
    icon: 'pi pi-exclamation-triangle',
    accept: async () => {
      const res = await api('/cpan/statistics/list/delete?id=' + item.id)
      if (res.ok) {
        toast.add({ severity: 'success', summary: 'Позиция спроса удалена', life: 3000 })
      }
    },
    reject: () => {
      //   toast.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected', life: 3000 })
    }
  })
}

async function onRowExpand({ data }) {
  // data: query, total
}
</script>
<template>
  <div class="container lg:px-20">
    <ConfirmPopup></ConfirmPopup>
    <div class="flex justify-between items-center">
      <div class="flex space-x-2 items-center font-semibold">
        <span>Нет в наличии</span>
        <InputSwitch v-model="isNoStock" />
      </div>
      <div class="">
        <StatsDownload />
      </div>
      <div class="flex justify-end mb-3">
        <InputText placeholder="Поиск" v-model="searchvalue" />
        <Button @click="search" :loading="isFetching" icon="pi pi-search" />
      </div>
    </div>

    <!-- <a :href="'/goods?searchvalue=' + slotProps.option.query" target="_blank"><Button rounded icon="pi pi-external-link" text size="small" /></a> -->

    <DataTable table-class="table-default" 
      :pt="{
        root: 'border-0 border-none shadow-xl shadow-slate-200 dark:shadow-none rounded-md'
      }"
      :loading="isFetching"
      v-model:expandedRows="expandedRows"
      stripedRows
      :value="list"
      tableStyle="min-width: 50rem"
      @rowExpand="onRowExpand"
    >
      <Column expander style="width: 5rem" />

      <Column field="query" header="Наименование">
        <template #body="{ data, field }">
          <a class="hover:underline" :href="'/goods?searchvalue=' + data.query" target="_blank">
            {{ data[field] }}
          </a>
        </template>
      </Column>
      <Column field="total" header="Всего"></Column>

      <Column field="product" header="Код">
        <template #body="{ data, field, column }">
          <a class="hover:underline" :href="'/goods?searchvalue=' + data.query" target="_blank">
            {{ data.query != data[field]?.prod_sku ? data[field]?.prod_sku : data[field]?.prod_analogsku }}
          </a>
        </template>
      </Column>
      <Column field="product" header="Размер">
        <template #body="{ data, field, column }"> {{ data[field]?.prod_size }} </template>
      </Column>
      <Column sortable field="product" header="Бренд">
        <template #body="{ data, field, column }"> {{ data[field]?.prod_manuf }}? </template>
      </Column>
      <Column sortable field="product" header="Материал">
        <template #body="{ data, field, column }"> {{ data[field]?.prod_material }} </template>
      </Column>
      <Column sortable field="product" header="Тип">
        <template #body="{ data, field, column }">
          {{ data[field]?.prod_type }}
        </template>
      </Column>
      <template #expansion="{ data }">
        <!-- {{ data }} -->
        <StatlistItemInfo :query="data.query" />
      </template>
      <!-- <Column field="category" header="Category"></Column> -->
      <!-- <Column field="quantity" header="Quantity"></Column> -->
    </DataTable>

    <!-- <ProgressBar v-if="isFetching" mode="indeterminate" style="height: 6px"></ProgressBar> -->
    <!-- <Listbox empty-message="-" empty-filter-message="-" filter-placeholder="Поиск" listStyle="height:700px" v-model="selected" :options="list?.data" optionLabel="query" class="w-full">
      <template #option="slotProps">
        <div class="flex justify-between">
          <div>
            <div class="flex flex-wrap justify-start items-center">
              <div class="flex space-x-4 items-end">
                <div class="w-40">
                  <b>{{ slotProps.option.query }}</b> -
                  <span class="text-blue-600">
                    <span v-if="slotProps.option.count != slotProps.option.total" class="text-gray-500 dark:text-gray-200 text-sm">({{ slotProps.option.count }})</span>
                    {{ slotProps.option.total }}</span
                  >
                </div>
                <div>
                  <span class="capitalize">{{ slotProps.option.client_ip }}</span>
                  <span class="text-sm text-gray-500 dark:text-gray-200"> ({{ dayjs(slotProps.option.created_at).locale('ru-RU').format('D.MM.YYYY') }}) </span>
                </div>
              </div>
            </div>
            <div v-if="slotProps.option.client_info" class="text-sm text-gray-500 dark:text-gray-200">({{ slotProps.option.client_info }})</div>
          </div>
          <div class="flex">
            <a :href="'/goods?searchvalue=' + slotProps.option.query" target="_blank"><Button rounded icon="pi pi-external-link" text size="small" /></a>
            <Button @click="deleteHandler($event, slotProps.option)" rounded severity="danger" icon="pi pi-times" text size="small" />
          </div>
        </div>
      </template>
    </Listbox> -->
    <!-- <Paginator @page="onPageHandler" :rows="limit" :totalRecords="list?.meta?.total || 0" :rowsPerPageOptions="[30, 100, 200]"></Paginator> -->
    <!-- Всего строк: {{ list?.meta?.total }} -->
  </div>
</template>
