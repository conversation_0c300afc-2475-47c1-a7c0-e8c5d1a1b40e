<script lang="ts" setup>
import { api } from '@/lib/_api'
import Button from 'primevue/button'
import Editor from 'primevue/editor'
import InputText from 'primevue/inputtext'
import InputSwitch from 'primevue/inputswitch'

import { useToast } from 'primevue/usetoast'
import { ref } from 'vue'

const props = defineProps<{
  to?: string
  subject?: string
  message?: string
  orderId?: number
}>()

const toast = useToast()

const orderId = ref(props.orderId)
const to = ref(props.to)
const subject = ref(props.subject || '')
const message = ref(props.message)
const applyDetails = ref(true)
const orderDetails = ref('')

const loading = ref(false)

const emit = defineEmits(['onSuccess'])

async function send() {
  toast.add({
    severity: 'info',
    'summary': 'Отправка сообщения...'
  })

  if (applyDetails.value) {
    await loadOrderDetails()
  }

  loading.value = true

  const res = await api('service/sendmail/', {
    method: 'POST',
    data: {
      to: to.value,
      message: message.value + orderDetails.value,
      subject: subject.value || ''
    }
  })

  toast.removeAllGroups()

  if (res.ok) {
    toast.add({
      severity: 'success',
      'summary': 'Сообщение отправлено'
    })

    emit('onSuccess', { to: to.value, subject: subject.value, message: message.value })
  } else {
    toast.add({
      severity: 'error',
      'summary': 'Серверная ошибка: ' + res.statusText
    })
  }

  loading.value = false
}

async function clearOrderDetils() {
  orderDetails.value = ''
}

async function loadOrderDetails() {
  const { body, statusText } = await api(`/cpan/forms/make?key=order_details&orderId=${orderId.value}`)

  if (body?.html) {
    orderDetails.value = ` <br> ${body.html}`
  } else {
    toast.add({
      severity: 'error',
      'summary': 'Ошибка формирования шаблона "Детали заказа": ' + statusText
    })
  }
}

async function orderDetailHandler() {
  applyDetails.value ? await loadOrderDetails() : clearOrderDetils()
}
</script>

<template>
  <div class="flex flex-col items-end space-y-4">
    <div class="flex items-center space-x-2">
      <span class="text-slate-500 dark:text-gray-300 font-semibold">Получатель: </span>
      <InputText v-model="to" />
    </div>
    <div class="flex items-center space-x-2">
      <span class="text-slate-500 dark:text-gray-300 font-semibold">Тема: </span>
      <InputText class="w-72" v-model="subject" />
    </div>
    <div class="mt-3 fflex items-center sspace-x-2">
      <span class="text-slate-500 dark:text-gray-300 font-semibold">Сообщение: </span>
      <Editor v-if="!props.message?.length" v-model="message" editorStyle="height: 320px" />
      <div @input="(e) => (message = e.target.innerText)" v-else v-html="message" contenteditable="true"></div>
    </div>
    <div class="flex justify-end items-center gap-10">
      <div class="flex gap-3 items-center">
        <span class="text-slate-500 dark:text-gray-300 font-semibold">Прикрепить детали заказа</span>
        <InputSwitch @change="orderDetailHandler" v-model="applyDetails" />
      </div>
      <Button :loading="loading" @click="send" icon="pi pi-check" label="Отправить" />
    </div>
  </div>
</template>
