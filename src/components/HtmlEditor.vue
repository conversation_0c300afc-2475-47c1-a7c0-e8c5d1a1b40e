<script setup lang="ts">
import { onMounted, ref, shallowRef, watch } from 'vue'
import { Codemirror } from 'vue-codemirror'
// import { javascript } from '@codemirror/lang-javascript'
import { html } from '@codemirror/lang-html'
// import { oneDark } from '@codemirror/theme-one-dark'

const code = ref('')
const emit = defineEmits(['change'])

const props = defineProps<{
  value: string
}>()

const extensions = [html()]

// Codemirror EditorView instance ref
const view = shallowRef()

const handleReady = (payload) => {
  view.value = payload.view
}

// Status is available at all times via Codemirror EditorView
const getCodemirrorStates = () => {
  const state = view.value.state
  const ranges = state.selection.ranges
  const selected = ranges.reduce((r, range) => r + range.to - range.from, 0)
  const cursor = ranges[0].anchor
  const length = state.doc.length
  const lines = state.doc.lines
  // more state info ...
  // return ...
}

watch([props], () => {
  props.value && (code.value = props.value)
})
</script>

<template>
  <Codemirror
    v-model="code"
    placeholder="HTML содержимое формы"
    :autofocus="true"
    :indent-with-tab="true"
    :tab-size="2"
    :extensions="extensions"
    @ready="handleReady"
    @change="(e) => emit('change', e)"
  />
</template>
