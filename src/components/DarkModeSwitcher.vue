<script setup lang="ts">
import { onMounted, ref } from 'vue'

let isDarkMode = ref(false)
const THEME_KEY = 'themePreference'
let isTheme

async function setDarkTheme(isDark) {
  isDarkMode.value = isDark
  document.documentElement.classList.toggle('dark', isDark)

  //   if (isDark) {
  //     await import ('../lib/css/custom-dark.css?' + Math.random())
  //   } else {
  //     await import ('../lib/css/custom-light.css?'+ Math.random())
  //   }
}

function toggleDarkMode() {
  setDarkTheme(!isDarkMode.value)
  window.localStorage.setItem(THEME_KEY, isDarkMode.value ? 'dark' : 'light')
}

onMounted(() => {
  const theme = window.localStorage.getItem(THEME_KEY)
  theme === 'dark' ? (isTheme = true) : (isTheme = false)
  setDarkTheme(theme === 'dark')
})
</script>

<template>
  <button class="outline-none focus:outline-none" @click="toggleDarkMode">
    <svg v-if="isDarkMode" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
    </svg>
    <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
      />
    </svg>
  </button>
</template>

<style>
.icon {
  @apply w-6 h-6
        stroke: currentColor
        stroke-width: 2
        stroke-linecap: round
        stroke-linejoin: round
        stroke-miterlimit: 10
        fill: none;
}
</style>
