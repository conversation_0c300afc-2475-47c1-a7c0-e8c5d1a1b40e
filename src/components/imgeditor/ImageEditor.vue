<template>
  <div>
    <div style="height: 600px">
      <div class="bg-white dark:bg-zinc-900 shadow-lg rounded-lg border-0" ref="editorContainer"></div>
    </div>

    <div class="flex space-x-3 mt-3">
      <Button class="p-button-text" icon="pi pi-save" @click="saveHandler" label="Сохранить" />
      <!-- <FileUpload class="p-button-text" auto mode="basic" accept="image/*" maxFileSize="1000000" chooseLabel="Выберите файл" :customUpload="true" @uploader="handleFileUpload" /> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, defineProps, defineEmits, watch, onMounted} from 'vue'
// import FileUpload from 'primevue/fileupload'

import ImageEditor from 'tui-image-editor'

import './photoeditor_light.css'

import {whiteTheme} from './themes'
import Button from 'primevue/button'

const props = defineProps({
  imageSrc: String,
  imgFile: File
})

const emit = defineEmits(['save'])

const image = ref(new Image())
const imageSrc = ref(props.imageSrc)
const editorContainer = ref()
const imageEditor = ref<ImageEditor>()

const locale_ru_RU = {
  // override default English locale to your custom
  Crop: 'Обзрезать',
  Resize: 'Изм.размер',
  Flip: 'Отразить',
  Rotate: 'Повернуть',
  Text: 'Текст',
  ZoomIn: 'Увеличить',
  ZoomOut: 'Уменьшить',
  Hand: 'Просмотр',
  History: 'Лог',
  Undo: 'Отменить',
  Redo: 'Повторить',
  Reset: 'Сбросить',
  Delete: 'Удалить',
  DeleteAll: 'Удалить все',
  Load: 'Загрузить',
  Download: 'Применить',
  Custom: 'Выделить',
  Square: 'Квадрат',
  Apply: 'Применить',
  Cancel: 'Отменить'
}

onMounted(() => {
  // //console.log('props.imgFile:', props.imgFile)
  
  initEditor()
  loadImgFromFile(props.imgFile)
})

function saveHandler() {
  const res = imageEditor.value?.toDataURL()
  emit('save', res)
}

function initEditor() {
  imageEditor.value = new ImageEditor(editorContainer.value, {
    // cssMaxWidth: 900,
    // cssMaxHeight: 700,
    usageStatistics: false,
    selectionStyle: {
      cornerSize: 20,
      rotatingPointOffset: 70,
      transparentCorners: true
    },
    includeUI: {
      locale: locale_ru_RU,
      uiSize: {
        height: '100%',
        width: '100%'
      },
      theme: whiteTheme,
      // initMenu: 'crop',
      menuBarPosition: 'top',
      menu: ['crop', 'resize', 'flip', 'rotate', 'text']
    }
  })
}

watch(
  () => props.imageSrc,
  (newVal) => {
    imageSrc.value = newVal
  }
)

function handleFileUpload(event) {
  const reader = new FileReader()
  const file = event.files[0]

  reader.onloadend = async () => {
    imageSrc.value = reader.result

    // initEditor()
    // imageEditor.value?.loadImageFromFile(file)
    // imageEditor.value?.destroy()
    // initEditor()

    loadImgFromFile(file)
  }

  reader.readAsDataURL(file)
}

async function loadImgFromFile(file) {
  const {newWidth, newHeight, oldWidth, oldHeight} = await imageEditor.value?.loadImageFromFile(file)

  imageEditor.value?.ui.activeMenuEvent()

  imageEditor.value?.ui.resizeEditor({
    imageSize: {
      newWidth,
      newHeight,
      oldWidth,
      oldHeight
    },
    uiSize: {
      height: '100%',
      width: '100%'
    }
  })
}
</script>

<style scoped>
.crop-area {
  position: absolute;
  border: 2px dashed black;
  pointer-events: none;
}
</style>
