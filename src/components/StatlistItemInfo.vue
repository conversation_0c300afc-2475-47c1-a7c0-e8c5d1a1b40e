<script lang="ts" setup>
import { api } from '@/lib/_api'
import { useUserStore } from '@/stores/user'
import { useQuery } from '@tanstack/vue-query'
import dayjs from 'dayjs'
import Button from 'primevue/button'
import Column from 'primevue/column'
import ConfirmPopup from 'primevue/confirmpopup'
import DataTable from 'primevue/datatable'
import ProgressBar from 'primevue/progressbar'
import ProgressSpinner from 'primevue/progressspinner'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'


const props = defineProps<{
  query: string
}>()

const confirm = useConfirm()
const toast = useToast()
const { userData } = useUserStore()

const { isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryKey: ['statListItemInfo'],
  queryFn: async () => await loadData(),
  refetchOnWindowFocus: false,
  refetchOnMount: true
})

async function loadData() {
  const res = await api('/cpan/statistics/list/item?query=' + props.query)
  return res.body
}

function deleteHandler(event, item) {
  confirm.require({
    target: event.currentTarget,
    message: 'Подтвердит удаление',
    icon: 'pi pi-exclamation-triangle',
    accept: async () => {
      const res = await api('/cpan/statistics/list/delete?id=' + item.id)
      if (res.ok) {
        toast.add({ severity: 'success', summary: 'Позиция спроса удалена', life: 3000 })
        refetch()
      }
    },
    reject: () => {
      //   toast.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected', life: 3000 })
    }
  })
}
</script>
<template>
  <ConfirmPopup></ConfirmPopup>

  <div v-if="!isFetching">
    <!-- <div :key="item.id" v-for="item in data ?? []">{{ item.client_info }} / {{ item.count }}</div> -->
    <DataTable table-class="table-default"  :value="data" size="small">
      <Column field="created_at" header="Дата">
        <template #body="{ data, field }">
          {{ dayjs(data[field]).locale('ru-RU').format('DD.MM.YYYY') }}
        </template>
      </Column>
      <Column field="client_ip" header="Автор"></Column>
      <Column style="width: 120px; font-weight: 600; text-align: center" field="count" header="Кол-во"></Column>
      <Column field="client_info" header="Примечание"></Column>
      <Column v-if="userData.user_role == 'su'" field="action" header="">
        <template #body="{ data }">
          <Button @click="($event) => deleteHandler($event, data)" rounded label="X" size="small" severity="danger" outlined text />
        </template>
      </Column>
    </DataTable>
  </div>
  <div v-else>
    <ProgressBar mode="indeterminate" style="height: 6px"></ProgressBar>
  </div>
</template>
