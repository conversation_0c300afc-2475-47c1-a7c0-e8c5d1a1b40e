<script setup lang="ts">
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import dayjs from 'dayjs'
import { defineProps, reactive, ref, watch } from 'vue'

interface DataR {
  zadarmabalance: number
  smsrubalance: number
}

let zadarmabalance = ref(0),
  ordersToday = ref(0),
  val = ref(0),
  smsrubalance = ref(0)

const loadSMSru = async () => {
  const { body } = await api('/service/sms/balance')
  // smsbalance.value = body.balance
  return body?.balance
}

const loadZadarmaBalance = async () => {
  const { body } = await api('/service/zadarma', {
    method: 'POST',
    data: {
      method: '/v1/info/balance/'
      // payload: {}
    }
  })
  return body?.balance
}

const loadOrderStats = async () => {
  const { body } = await api('/cpan/statistics/orders/current')
  return body
}

const { isLoading, isError, data, error } = useQuery({
  queryKey: ['counters'],
  queryFn: async () => {
    const [zadarmabalance, smsrubalance, orderStats] = await Promise.all([await loadZadarmaBalance(), await loadSMSru(), await loadOrderStats()])
    return {
      zadarmabalance,
      smsrubalance,
      orderStats
    }
  },
  refetchOnMount: false,
  refetchOnWindowFocus: true,
  refetchOnReconnect: true
})


</script>

<template>
  <div class="flex justify-between /flex-wrap">
    <div class="dayly">
      <div class="flex justify-between">
        <div class="mx-2 flex items-center p-4 bg-white dark:bg-zinc-800 rounded-lg shadow-xs">
          <div class="p-3 mr-4 text-gray-500 dark:text-gray-200 bg-gray-200 rounded-full dark:bg-zinc-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"
              />
            </svg>
          </div>
          <div>
            <p class="mb-2 text-sm font-medium text-gray-600 dark:text-gray-200">Оборот <b>текущий</b></p>
            <p class="text-lg font-semibold text-gray-700 dark:text-gray-200">{{ Number(data?.orderStats?.sum).toLocaleString() }} руб.</p>
          </div>
        </div>
        <div class="mx-2 flex items-center p-4 bg-white dark:bg-zinc-800 rounded-lg shadow-xs ">
          <div class="p-3 mr-4 text-gray-500 dark:text-gray-200 bg-gray-200 rounded-full dark:bg-zinc-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"
              />
            </svg>
          </div>
          <div>
            <p class="mb-2 text-sm font-medium text-gray-600 dark:text-gray-200">Оборот <b>сегодня</b></p>
            <p class="text-lg font-semibold text-gray-700 dark:text-gray-200">{{ Number(data?.orderStats?.countToday).toLocaleString() }} руб.</p>
          </div>
        </div>
      </div>
    </div>
    <div class="total">
      <div class="flex justify-between">
        <div class="mx-2 flex items-center p-4 bg-white dark:bg-zinc-800 rounded-lg shadow-xs">
          <div class="p-3 mr-4 text-gray-500 dark:text-gray-200 bg-gray-200 rounded-full dark:bg-zinc-700">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div>
            <p class="mb-2 text-sm font-medium text-gray-600 dark:text-gray-200">SMS.ru баланс</p>
            <p class="text-lg font-semibold text-gray-700 dark:text-gray-200">{{ Number(data?.smsrubalance).toLocaleString() || '...' }} руб.</p>
          </div>
        </div>

        <div class="mx-2 flex items-center p-4 bg-white dark:bg-zinc-800 rounded-lg shadow-xs ">
          <div class="p-3 mr-4 text-gray-500 dark:text-gray-200 bg-gray-200 rounded-full dark:bg-zinc-700">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div>
            <p class="mb-2 text-sm font-medium text-gray-600 dark:text-gray-200 ">Zadarma баланс</p>
            <p class="text-lg font-semibold text-gray-700 dark:text-gray-200">{{ Number(data?.zadarmabalance).toLocaleString() || '...' }} руб.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
