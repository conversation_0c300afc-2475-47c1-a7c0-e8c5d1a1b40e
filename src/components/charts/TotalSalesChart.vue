<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Chart from 'primevue/chart'

const props = defineProps({
  salesCounters: {},
  prev_salesCounters: undefined,
  title: undefined,
  borderColorCurrent: undefined,
  borderColorPrevious: undefined
  // ordersCounters: []
})

let series = ref([]),
  salesCounters = ref(props.salesCounters),
  prev_salesCounters = ref(props.prev_salesCounters),
  loading = ref(true),
  // ordersCounters = ref(props.ordersCounters || []),
  title = ref(props.title)

const options = {
  // ...другие настройки графика...
  plugins: {
    tooltip: {
      callbacks: {
        label: (context) => {
          const label = context.dataset.label || ''
          const value = context.parsed.y

          if (context.dataset.label == 'Текущий') {
            const diff = calculateDifference(context.parsed.y, context.chart.data.datasets[1].data[context.dataIndex])
            return `${label}: ${value.toLocaleString('ru-RU', { style: 'currency', currency: 'RUB' })} (${diff > 0 ? '+' : ''} ${diff.toLocaleString('ru-RU', { style: 'currency', currency: 'RUB' })})`
          } else return `${label}: ${value.toLocaleString('ru-RU', { style: 'currency', currency: 'RUB' })}`
        }
      }
    }
  }
}

const basicData = ref({
  labels: Object.keys(salesCounters.value),
  datasets: [
    {
      label: 'Текущий',
      data: Object.values(salesCounters.value),
      fill: false,
      borderColor: props.borderColorCurrent || '#475569',
      borderWidth: 3,
      tension: 0.9
    },
    {
      label: Number(new Date().getFullYear()) - 1,
      data: Object.values(prev_salesCounters.value),
      fill: false,
      borderColor: props.borderColorPrevious || '#64748b',
      borderWidth: 1,
      tension: 0.4
    }
  ]
})

function calculateDifference(currentValue, otherValue) {
  return currentValue - otherValue
}
</script>

<template>
  <div>
    <h1 :style="'color: ' + props.borderColorCurrent || 'current'" class="text-2gray-500 font-semibold">{{ title || 'Сумма заказов' }}</h1>
    <!-- <Chart :canvasProps="{ height: 60 }" type="line" :data="basicData" /> -->
    <Chart :canvasProps="{ height: 60 }" type="line" :data="basicData" :options="options" />
  </div>
</template>
