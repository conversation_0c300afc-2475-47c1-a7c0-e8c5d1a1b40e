<script setup lang="ts">
import { ref } from 'vue'
import Chart from 'primevue/chart'

const props = defineProps({
  ordersCounters: {},
  // prev_ordersCounters: {}
  canceledOrdersCounters: {}
})

let options = ref(),
  series = ref(),
  ordersCounters = ref(props.ordersCounters),
  // prev_ordersCounters = ref(props.prev_ordersCounters),
  canceledOrdersCounters = ref(props.canceledOrdersCounters),
  loading = ref(true)

const stackedData = ref({
  labels: Object.keys(ordersCounters.value),
  datasets: [
    {
      type: 'bar',
      label: 'В работе',
      backgroundColor: '#475569',
      data: Object.values(ordersCounters.value)
    },
    {
      type: 'bar',
      label: 'Отмененные',
      backgroundColor: '#cbd5e1',
      data: Object.values(canceledOrdersCounters.value)
    }
  ]
})
//'#4c51bf', '#4a5568'
const stackedOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    tooltip: {
      mode: 'index',
      intersect: false
    },
    legend: {
      labels: {
        color: '#495057'
      }
    }
  },
  scales: {
    x: {
      stacked: true,
      ticks: {
        color: '#495057'
      },
      grid: {
        color: '#ebedef'
      }
    },
    y: {
      stacked: true,
      ticks: {
        color: '#495057'
      },
      grid: {
        color: '#ebedef'
      }
    }
  }
})
</script>

<template>
  <div>
    <h1 class="text-gray-500 dark:text-gray-200 font-semibold">Отменен/Выполнен</h1>
    <div class="flex justify-center">
      <Chart :canvasProps="{ height: 280 }" type="bar" :data="stackedData" :options="stackedOptions" />
    </div>
  </div>
</template>
