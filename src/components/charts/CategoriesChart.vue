<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import Dropdown from 'primevue/dropdown'
import Chart from 'primevue/chart'

const props = defineProps({
  byCategoriesCounters: {}
  //prev_categories: {}
})

let options = ref(),
  series = ref(),
  categories = ref(props.byCategoriesCounters),
  //prev_categories = ref(props.prev_categories),
  loading = ref(true),
  activeField = ref('price'),
  chartInstance = ref(),
  show = ref(true)

let activeFields = [
  { value: 'totalCount', title: 'По кол-ву' },
  { value: 'price', title: 'По сумме' }
]

function getData() {
  return Object.keys(categories.value)
    .map((key) => categories.value[key][activeField.value])
    .filter((i) => i)
}

const chartData = ref({
  labels: Object.keys(categories.value),
  datasets: [
    {
      data: getData(),
      height: 100,
      backgroundColor: ['#67e8f9', '#bef264', '#1d4ed8', '#4f46e5', '#9333ea', '#60a5fa', '#14b8a6', '#22c55e', '#eab308', '#94a3b8', '#581c87']
      // hoverBackgroundColor: ["#FF6384", "#36A2EB", "#FFCE56"]
    }
  ]
})

const lightOptions = ref({
  responsive: true,
  plugins: {
    legend: {
      labels: {
        color: '#495057'
      },
      position: 'right'
    }
  }
})

watch(activeField, () => {
  //console.log('active field wathc:', chartInstance.value)
  //console.log('activeField:', activeField.value)

  // show.value = false
  // chartInstance.value.reinit()
  // chartInstance.value.chart.destroy()
  // chartInstance.value.chart.render()

  chartData.value.datasets[0].data = getData()

  // show.value = true
})

onMounted(() => {})
</script>

<template>
  <div>
    <h1 class="text-gray-500 dark:text-gray-200 font-semibold">Продажи по категориям</h1>
    <div class="flex justify-center">
      <Chart :canvasProps="{ height: 150 }" ref="chartInstance" type="doughnut" :data="chartData" :options="lightOptions" />
    </div>
    <Dropdown v-model="activeField" :options="activeFields" optionLabel="title" optionValue="value" placeholder="Выбор" />
  </div>
</template>
