<script setup>
    import { onMounted, ref, watch } from 'vue'
    import Dropdown from 'primevue/dropdown'

    const props = defineProps({
        byCategoriesCounters: {},
        //prev_categories: {}
    })

    let options = ref(),
        series = ref(),
        categories = ref(props.byCategoriesCounters),
        //prev_categories = ref(props.prev_categories),
        loading = ref(true),
        activeField = ref('totalCount');

    let activeFields = [{value: 'totalCount', title: 'По кол-ву'}, {value: 'price', title: 'По сумме'}]

    // //console.log('Total orders PROPS: ', props)
    //console.log('Object.keys(categories.value):', Object.keys(categories.value))


    const load = () => {
        options.value = {
            chart: {
              type: 'pie',
            },
            title: {
              text: 'Продажи по категориям',
              align: 'left'
            },
/*             dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return Number(val).toFixed(1) + "%"
                }
            }, */
            labels: Object.keys(categories.value),
            responsive: [{
              breakpoint: 480,
              options: {
                legend: {
                  position: 'bottom'
                }
              }
            }],
            tooltip: {
              y: {
                formatter: function (val) {
                  let _pcs = val 
                  let _totalPrice = 1

                  Object.keys(categories.value).map(key => {
                      if (categories.value[key][activeField.value] == val) {
                          _totalPrice = categories.value[key].price
                          _pcs = categories.value[key].totalCount
                      }
                  })
                  
                  return `Кол-во: ${_pcs} шт., сумма: ${_totalPrice} р.`
                }
              }
            }
        }
        series.value = Object.keys(categories.value).map(key => categories.value[key][activeField.value]).filter(i => i)
    }

    watch(activeField, () => {
        //console.log('CHANGED: activeField: ' + activeField)
        series.value = Object.keys(categories.value).map(key => categories.value[key][activeField.value]).filter(i => i)
    })

    onMounted(() => {
        load()
        loading.value = false
    })

</script>


<template>
    <div v-if="!loading">
        <apexchart width="100%" height="450px" type="donut" :options="options" :series="series"></apexchart>
        <Dropdown v-model="activeField" :options="activeFields" optionLabel="title" optionValue="value" placeholder="Выбор" />
    </div>
</template>
