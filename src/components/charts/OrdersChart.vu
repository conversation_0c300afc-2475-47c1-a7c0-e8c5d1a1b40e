<script setup>
    import { onMounted, ref, watch } from 'vue'
    import InputSwitch from 'primevue/inputswitch';

    const props = defineProps({
        ordersCounters: {},
        prev_ordersCounters: undefined,
        title: '',
        prev: false
    })

    //console.log('Total orders PROPS: ', props)
    // //console.log('props.prev', props.prev);

    let options = ref(),
        series = ref(),
        AChart = ref(),
        showLastYear = ref(false),
        ordersCounters = ref(props.ordersCounters),
        prev_ordersCounters = ref(props.prev_ordersCounters),
        loading = ref(true),
        title = ref(props.title);

    const load = () => {
        options.value = {
            chart: {
              id: 'salesChart',
              height: 400,
              type: 'bar',
              // stacked: true,
              toolbar: {
                show: true
              },
              dropShadow: {
                enabled: true,
                color: '#000',
                top: 14,
                left: 5,
                blur: 10,
                opacity: 0.1
              },
              toolbar: {
                show: false
              }
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '90%',
                barHeight: '80%',
                endingShape: 'rounded',
                // distributed: true,
              },
            },
            colors: ['#22D3EE', '#4a5568'],
            dataLabels: {
              enabled: true,
              style: {
                fontSize: "14px",
                fontFamily: "Helvetica, Arial, sans-serif",
                // fontWeight: "bold"
              }
            },
            title: {
              text: title.value || 'Продажи',
              align: 'left'
            },
            grid: {
              borderColor: '#e7e7e7',
              row: {
                colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
                opacity: 0.5
              },
            },
            markers: {
              size: 2
            },
            xaxis: {
              categories: Object.keys(ordersCounters.value),
            },
            legend: {
              fontSize: "18px",
              position: 'top',
              horizontalAlign: 'right',
              floating: true,
              offsetY: -25,
              offsetX: -5
            }
        }

        series.value = [
            {
                name: new Date().getFullYear(),
                data: Object.values(ordersCounters.value)
            }
        ]

    }

    watch(showLastYear, () => {
      //console.log('showLastYear:', showLastYear.value);

      // серии реактивные, updateSeries не нужен
      AChart.value.chart.updateSeries(
        showLastYear.value ? 
            [...series.value,             {
                name: (Number(new Date().getFullYear()) - 1),
                data: prev_ordersCounters.value && Object.values(prev_ordersCounters.value) 
            }] 
            : series.value)
    })

    onMounted(() => {
        load()
        loading.value = false
    })

</script>


<template>
    <div v-if="!loading">
        <apexchart ref="AChart" width="100%" height="420px" type="bar" :options="options" :series="series"></apexchart>
        <div v-if="prev_ordersCounters" class="flex items-center space-x-3 text-gray-600 dark:text-gray-200"><span>{{(Number(new Date().getFullYear()) - 1)}} год </span><InputSwitch v-model="showLastYear" /></div>
    </div>
</template>
