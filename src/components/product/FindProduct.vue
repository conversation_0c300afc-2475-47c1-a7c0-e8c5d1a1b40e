<script setup lang="ts">
import { ref } from 'vue'
import { api } from '../../lib/_api'
import AutoComplete from 'primevue/autocomplete'
import Checkbox from 'primevue/checkbox'
import <PERSON><PERSON> from 'primevue/button'
import Dialog from 'primevue/dialog'
import ProductCard from './ProductCard.vue'

const emit = defineEmits(['select'])

const props = defineProps({
  deepSwitch: Boolean,
  previewProductTarget: String
})

const appendTo = ref(props.previewProductTarget || '.find-product-component')

//console.log('🚀 ~ file: FindProduct.vue:15 ~ deep:', props)

const filterModel = ref()
const filteredProds = ref([])
const deepSearch = ref(false)
const showProductCard = ref(false)
const modalProduct = ref({})

async function findItemHandler({ query: value }) {
  if (String(value).length > 3) {
    // const { body } = await api('/cpan/products/?' + 'searchvalue=' + value)
    const { body } = await api(`/search/${deepSearch.value ? 'global' : 'fast'}/` + value)

    if (deepSearch.value) {
      filteredProds.value = body.searchresults?.map((item) => item.products)?.flat()
    } else {
      filteredProds.value = body.products //.data
    }
  } else {
    filteredProds.value = []
  }
}

function selectHandler(e) {
  // //console.log('🚀 ~ file: FindProduct.vue:33 ~ selectHandler ~ e:', e)
  // //console.log('classList:', e.originalEvent?.target?.classList)

  if (e.originalEvent?.target?.classList?.contains('pi')) {
    showProductCard.value = true
    modalProduct.value = e.value

    e.originalEvent?.preventDefault()
    e.originalEvent?.stopPropagation()

    // throw new Error('test error')
  } else {
    emit('select', e.value)
    filterModel.value = null
  }
}
</script>

<template>
  <div class="find-product-component items-stretch">
    <AutoComplete
      dataKey="prod_id"
      :forceSelection="false"
      v-model="filterModel"
      :suggestions="filteredProds"
      @item-select="selectHandler($event)"
      @complete="findItemHandler($event)"
      placeholder="Найти товар"
      field="prod_analogsku"
      inputClass="w-full"
      :dropdown="true"
      dropdownMode="current"
    >
      <template #item="slotProps">
        <div class="flex items-center justify-left space-x-2">
          <!-- <img class="w-16 rounded-md shadow" :alt="slotProps.item.prod_img" :src="'https://mirsalnikov.ru/data/rti/' + slotProps.item.prod_img + '.jpg'" /> -->
          <div class="font-bold text-gray-700">{{ slotProps.item.prod_analogsku }}</div>
          <div class="text-gray-700">{{ slotProps.item.prod_sku }}</div>
          <div class="text-gray-600 dark:text-gray-200">{{ slotProps.item.prod_size }}</div>
          <div class="text-gray-600 dark:text-gray-200">{{ slotProps.item.prod_manuf }}</div>
          <div class="text-slate-800">Склад: {{ slotProps.item.prod_count }}</div>
          <!-- <Button @click.prevent="($event) => selectProduct($event, slotProps.item)" text icon="pi pi-eye" rounded /> -->
          <i class="pi pi-eye p-1 hover:dark:bg-slate-700 rounded-full" />
        </div>
      </template>
    </AutoComplete>
    <div v-if="props.deepSwitch" class="ttext-center text-slate-600 dark:text-zinc-100 font-semibold py-2 rounded-lg p-1">
      <span>Глубокий поиск </span>
      <Checkbox v-model="deepSearch" :binary="true" />
    </div>
  </div>

  <div v-if="showProductCard">
    <Dialog dismissableMask :append-to="appendTo" modal maximizable class="w-4/5" :header="modalProduct.prod_purpose + ' ' + modalProduct.prod_analogsku" v-model:visible="showProductCard">
      <ProductCard @update="(e:any) => (showProductCard = false)" :productData="modalProduct"></ProductCard>
    </Dialog>
  </div>
</template>
