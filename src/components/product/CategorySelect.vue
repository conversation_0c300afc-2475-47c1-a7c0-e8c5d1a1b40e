<script setup lang="ts">
import { api } from '@/lib/_api'
import type { ApiInitData, RootCategory } from '@/lib/interfaces/ApiSettings'
import { useSettingsStore } from '@/stores/apisettings'
import { useAppStore } from '@/stores/app'
import AutoComplete from 'primevue/autocomplete'
import { onMounted, ref, watch } from 'vue'

const { appStoreData, setAppStoreData } = useAppStore()

const props = defineProps({
  onChangeHandler: Function,
  multiple: Boolean,
  initValue: Number,
  initState: Object
})

interface SCat {
  label: string
  value: number
}

const value = ref(props.initState)
const suggestions = ref<SCat[]>([])
const filteredItems = ref<SCat[]>()

async function appInit() {
  const apiInitDataRes = await api('/cpan/init')
  if (apiInitDataRes.ok) {
    const apiInitData: ApiInitData = apiInitDataRes.body
    setAppStoreData({ categories: apiInitData.categories })
  } else {
    // throw error
  }
}

function search(event: any) {
  if (!event.query.trim().length) {
    filteredItems.value = [...suggestions.value]
  } else {
    filteredItems.value = suggestions.value.filter((item: any) => {
      return item.label.toLowerCase().startsWith(event.query.toLowerCase())
    })
  }
}

let _cats: RootCategory[] = []

watch(appStoreData, (value) => {
  if (value) {
    value?.categories.rootCategories.map((cat) => {
      const res = {
        ...cat
      }

      delete res.subcategories

      if (cat.subcategories?.length) {
        _cats.push(cat.subcategories)
      }

      _cats.push(res)
    })

    suggestions.value = _cats.flat().map((i) => ({ label: i.cat_title, value: i.cat_id }))
  }
})

watch([props, suggestions], ([_props, _suggestions]) => {
  // //console.log('file: CategorySelect.vue:70 ~ watch ~ props:', props)
  if (typeof _props.initValue !== 'undefined') {
    const x = _suggestions.find((x) => x.value == _props.initValue)
    // //console.log('@@@x: ', x)

    value.value = props.multiple ? [x] : x
  }
})

onMounted(async () => {
  await appInit()
})
</script>

<template>
  <AutoComplete @change="({ value }) => props.onChangeHandler?.(value)" @complete="search" dropdown optionLabel="label" v-model="value" :multiple="props.multiple" :suggestions="filteredItems" />
</template>
