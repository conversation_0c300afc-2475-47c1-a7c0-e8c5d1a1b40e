import { createTRPCClient, httpBatchLink } from '@trpc/client'
import type { AppRouter } from '../../rti-api/app/Services/tRPC'

export const trpc = createTRPCClient<AppRouter>({
  links: [
    httpBatchLink({
      url: (import.meta.env.DEV ? import.meta.env['VITE_API_URL_DEV'] : import.meta.env['VITE_API_URL']) + '/trpc', //import.meta.env.DEV ? '/trpc' : import.meta.env['VITE_API_URL'] + '/trpc',
      async headers() {
        return {
          authorization: 'Bearer ' + window.localStorage.getItem('token')
        }
      }
    })
  ]
})
