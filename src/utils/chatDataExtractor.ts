import type { Message, MessageToolCall, GenerateDocToolInput, FindOrgToolInput, SearchToolInput } from '@/lib/interfaces/Agent'

/**
 * Интерфейс для извлеченных данных из чата
 */
export interface ExtractedChatData {
  client?: {
    fullorgname?: string
    client_name?: string
    client_mail?: string
    client_phone?: string
    client_country?: string
    client_city?: string
    client_street?: string
    client_house?: string
    client_postindex?: string
  }
  org?: {
    org_name?: string
    org_adress?: string
    org_inn?: string
    org_kpp?: string
    org_rschet?: string
    org_kschet?: string
    org_bik?: string
    org_bank?: string
  }
  products?: Array<{
    id: number
    qty: number
    product: {
      prod_sku: string
      prod_analogsku: string
      prod_price: number
      prod_manuf: string
      prod_type: string
      prod_size: string
      prod_purpose: string
    }
  }>
  shippingData?: {
    countryId?: number
    shippingIndex?: number
    shippingType?: 'express' | 'standard'
  }
}

/**
 * Извлекает данные из tool calls в сообщениях чата
 */
export function extractDataFromMessages(messages: Message[]): ExtractedChatData {
  const extractedData: ExtractedChatData = {}

  // Проходим по всем сообщениям в обратном порядке (от новых к старым)
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i]
    
    if (!message.toolCalls || message.toolCalls.length === 0) {
      continue
    }

    // Обрабатываем каждый tool call
    for (const toolCall of message.toolCalls) {
      extractDataFromToolCall(toolCall, extractedData)
    }
  }

  return extractedData
}

/**
 * Извлекает данные из конкретного tool call
 */
function extractDataFromToolCall(toolCall: MessageToolCall, extractedData: ExtractedChatData): void {
  switch (toolCall.toolName) {
    case 'generateDocTool':
      extractGenerateDocData(toolCall, extractedData)
      break
    case 'findOrgTool':
      extractFindOrgData(toolCall, extractedData)
      break
    case 'searchTool':
      extractSearchData(toolCall, extractedData)
      break
  }
}

/**
 * Извлекает данные из generateDocTool
 */
function extractGenerateDocData(toolCall: MessageToolCall, extractedData: ExtractedChatData): void {
  const input = toolCall.input as GenerateDocToolInput
  
  if (input) {
    // Извлекаем данные клиента
    if (input.client && !extractedData.client) {
      extractedData.client = { ...input.client }
    }
    
    // Извлекаем данные организации
    if (input.org && !extractedData.org) {
      extractedData.org = { ...input.org }
    }
    
    // Извлекаем данные товаров
    if (input.productData && input.productData.length > 0 && !extractedData.products) {
      extractedData.products = [...input.productData]
    }
    
    // Извлекаем данные доставки
    if (!extractedData.shippingData) {
      extractedData.shippingData = {
        countryId: input.countryId,
        shippingIndex: input.shippingIndex,
        shippingType: input.shippingType
      }
    }
  }
}

/**
 * Извлекает данные из findOrgTool
 */
function extractFindOrgData(toolCall: MessageToolCall, extractedData: ExtractedChatData): void {
  const output = toolCall.output
  
  if (output && output.org && !extractedData.org) {
    extractedData.org = {
      org_name: output.org.org_name,
      org_adress: output.org.org_adress,
      org_inn: output.org.org_inn,
      org_kpp: output.org.org_kpp,
      org_rschet: output.org.org_rschet,
      org_kschet: output.org.org_kschet,
      org_bik: output.org.org_bik,
      org_bank: output.org.org_bank
    }
  }
}

/**
 * Извлекает данные из searchTool
 */
function extractSearchData(toolCall: MessageToolCall, extractedData: ExtractedChatData): void {
  const output = toolCall.output
  
  if (output && output.products && output.products.length > 0 && !extractedData.products) {
    // Преобразуем найденные товары в формат для счета
    extractedData.products = output.products.map((product: any) => ({
      id: product.prod_id,
      qty: 1, // По умолчанию количество 1
      product: {
        prod_sku: product.prod_sku,
        prod_analogsku: product.prod_analogsku,
        prod_price: product.prod_price,
        prod_manuf: product.prod_manuf,
        prod_type: product.prod_type,
        prod_size: product.prod_size,
        prod_purpose: product.prod_purpose
      }
    }))
  }
}

/**
 * Извлекает последние данные организации из сообщений
 */
export function extractLatestOrgData(messages: Message[]): ExtractedChatData['org'] | null {
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i]
    
    if (!message.toolCalls) continue
    
    for (const toolCall of message.toolCalls) {
      if (toolCall.toolName === 'findOrgTool' && toolCall.output?.org) {
        return toolCall.output.org
      }
      
      if (toolCall.toolName === 'generateDocTool' && toolCall.input?.org) {
        return toolCall.input.org
      }
    }
  }
  
  return null
}

/**
 * Извлекает последние данные клиента из сообщений
 */
export function extractLatestClientData(messages: Message[]): ExtractedChatData['client'] | null {
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i]
    
    if (!message.toolCalls) continue
    
    for (const toolCall of message.toolCalls) {
      if (toolCall.toolName === 'generateDocTool' && toolCall.input?.client) {
        return toolCall.input.client
      }
    }
  }
  
  return null
}

/**
 * Извлекает все найденные товары из сообщений
 */
export function extractAllProducts(messages: Message[]): ExtractedChatData['products'] | null {
  const allProducts: ExtractedChatData['products'] = []
  
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i]
    
    if (!message.toolCalls) continue
    
    for (const toolCall of message.toolCalls) {
      if (toolCall.toolName === 'searchTool' && toolCall.output?.products) {
        const products = toolCall.output.products.map((product: any) => ({
          id: product.prod_id,
          qty: 1,
          product: {
            prod_sku: product.prod_sku,
            prod_analogsku: product.prod_analogsku,
            prod_price: product.prod_price,
            prod_manuf: product.prod_manuf,
            prod_type: product.prod_type,
            prod_size: product.prod_size,
            prod_purpose: product.prod_purpose
          }
        }))
        allProducts.push(...products)
      }
      
      if (toolCall.toolName === 'generateDocTool' && toolCall.input?.productData) {
        allProducts.push(...toolCall.input.productData)
      }
    }
  }
  
  return allProducts.length > 0 ? allProducts : null
}