// generated by https://transform.tools/json-to-typescript

export interface ApiInitData {
  productDBfields: string[]
  settings: Settings
  categories: Categories
}

export interface Settings {
  currency: Currency
  client_columns: string
  stripekeyclient_test: string
  'eu.shipping.basePrice': number
  'eu.shipping.step.maxStartOrderSum': number
  'eu.shipping.step.shippingPrice': number
  'eu.shipping.whosale.start': number
  'eu.shipping.whosale.basePrice': number
  'eu.shipping.whosale.step.orderSum': number
  'eu.shipping.whosale.step.shippingPrice': number
  'eu.shipping.step.orderSum': number
  'pl.shipping.step.maxStartOrderSum': number
  'pl.shipping.basePrice': number
  'pl.shipping.step.orderSum': number
  'pl.shipping.step.shippingPrice': number
  paypalkeyclient: string
  DISCOUNT_START_SUM: number
  BIG_DISCOUNT_VALUE: number
  google_analytics_key: string
  stripekeyclient: string
  stripeapi: string
  stripeapi_test: string
  google_analytics_key_rti: number
  'rumisota.retail': string
  'pochta.destination.from': number
  'pochta.object.standart.ru': number
  'pochta.object.standart.all': number
  'pochta.object.express.ru': number
  'pochta.object.express.all': number
  'pochta.standart.addnl.ru': number
  'pochta.standart.addnl.all': number
  'pochta.express.addnl%.ru': number
  'pochta.express.addnl%.all': number
  'products.priceCurrency': number
  'pochta.standard.maxValue': number
  BIG_DISCOUNT_START_SUM: number
  'products.buylimit': string
  'yandex.turbo.rss.cdata': string
  'yandex.turbo.rss.link': string
  'yandex.turbo.rss.sitelink': string
  'yandex.turbo.rss.description': string
  'backup_yandex.turbo.rss.cdata': string
  types_conformity: TypesConformity[]
  AliApiToken: string
  smsru_api_token: string
  zadarma_api_key: string
  zadarma_api_secret: string
  senders: Sender[]
  cataliases: any
  telegram_secret_phrase: number
}

export interface Currency {
  eur: number
  zl: number
  r_eur: number
  r_zl: number
}

export interface TypesConformity {
  TCS: string
  Corteco: string
  'Simrit-Freudenberg': string
  National: any
  NOK: string
  Dichtomatik: string
  Kaco: string
  'SKF/CR': string
  SOG: string
  Teleborg: string
  ERIKS: string
  Goetze: string
  NAK: string
  Timken: string
  Elring: string
  DPH: string
  Athena: string
  TTO: string
  Ariete: string
}

export interface Sender {
  name: string
  address: string
  birthdate?: string
  index: number
  phone: string
}

export interface Categories {
  rootCategories: RootCategory[]
  childCategories: ChildCategory[]
}

export interface RootCategory {
  cat_id: number
  cat_title: string
  cat_pic: string
  cat_sort: number
  cat_search_sort: number
  cat_rootcat: number
  cat_active: number
  cat_url: string
  cat_note: string
  imagesize?: string
  isnormal: number
  subcategories: Subcategory[]
}

export interface Subcategory {
  cat_id: number
  cat_title: string
  cat_pic: string
  cat_sort: number
  cat_search_sort: number
  cat_rootcat: number
  cat_active: number
  cat_url: string
  cat_note?: string
  imagesize: string
  isnormal: number
  subcategories: any[]
}

export interface ChildCategory {
  cat_id: number
  cat_title: string
  cat_pic: string
  cat_sort: number
  cat_search_sort: number
  cat_rootcat: number
  cat_active: number
  cat_url: string
  cat_note?: string
  imagesize: string
  isnormal: number
  subcategories: any[]
}
