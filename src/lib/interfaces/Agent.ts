// Типы для работы с Mastra orgManagerAgent

// Базовые типы для инструментов агента
export type ToolName = 'searchTool' | 'findOrgTool' | 'generateDocTool'

export interface ToolCall {
  displayId: string;
  toolCallId: string;
  toolName: string;
  input?: any;
  output?: any;
  status: 'executing' | 'success' | 'error';
}

// Типы для входных данных инструментов
export interface SearchToolInput {
  queries: Array<{
    q: string
    filters?: string
    sort?: string
  }>
}

export interface FindOrgToolInput {
  query: string // Название организации или ИНН
}

export interface GenerateDocToolInput {
  countryId?: number
  shippingIndex?: number
  shippingType?: 'express' | 'standard'
  productData: Array<{
    id: number
    qty: number
    product: {
      prod_sku: string
      prod_analogsku: string
      prod_price: number
      prod_manuf: string
      prod_type: string
      prod_size: string
      prod_purpose: string
    }
  }>
  client: {
    fullorgname: string
    client_name: string
    client_mail: string
    client_phone: string
    client_country?: string
    client_city?: string
    client_street?: string
    client_house?: string
    client_postindex?: string
  }
  org: {
    org_name: string
    org_adress?: string
    org_inn?: string
    org_kpp?: string
    org_rschet?: string
    org_kschet?: string
    org_bik?: string
    org_bank?: string
  }
}

// Типы для запросов к агенту
export interface AgentRequest {
  message: string
  threadId?: string
  attachments?: File[]
}

export interface AgentStreamRequest {
  message: string
  threadId: string
  files?: File[]
}

// Типы для стриминга ответа
export type StreamChunk =
  | { type: 'text'; content: string }
  | { type: 'error'; error: string }
  | { type: 'tool-start'; toolCall: Omit<ToolCall, 'output' | 'status'> & { status: 'executing' } }
  | { type: 'tool-output'; toolCall: Pick<ToolCall, 'toolCallId' | 'output'> };


// Типы для ответов агента
export interface GeneratedFile {
  url: string
  fileName: string
  type: 'xlsx' | 'pdf'
  size?: number
  generatedAt?: Date
}

export interface AgentResponse {
  message: string
  threadId: string
  files?: GeneratedFile[]
  toolCalls?: ToolCall[]
  metadata?: {
    processingTime?: number
    tokensUsed?: number
  }
}

// Типы для сессий чата
export interface ChatSession {
  id: string
  title: string
  createdAt: string
  updatedAt: string
  messageCount: number
  lastMessage?: string
  clientId?: number
  clientEmail?: string
  clientName?: string
  leadStatus?: 'new' | 'contacted' | 'qualified' | 'proposal' | 'closed'
  metadata?: {
    type: 'orgAgent_chat'
    tags?: string[]
    clientInfo?: {
      orgName?: string
      inn?: string
      phone?: string
    }
  }
}

export interface SessionFilters {
  dateFrom?: Date
  dateTo?: Date
  hasFiles?: boolean
  clientOrg?: string
}

// Типы для сообщений
export interface ChatMessage {
  id: string
  threadId: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  attachments?: MessageAttachment[]
  files?: GeneratedFile[]
  toolCalls?: ToolCall[]
  metadata?: {
    processingTime?: number
    tokensUsed?: number
    error?: string
  }
}

export interface MessageAttachment {
  id: string
  name: string
  size: number
  mimeType: string
  url?: string
}

// Типы для клиентов
export interface ChatClient {
  client_id: number
  client_name: string
  client_mail: string
  client_phone?: string
  client_number?: number
  client_city?: string
  client_country?: string
  org?: {
    org_id?: number
    org_name?: string
    org_inn?: string
    org_kpp?: string
  }
}

// Типы для UI компонентов
export interface MessageToolCall {
  displayId: string
  toolCallId: string
  toolName: string
  input: any
  output: any
  status: 'executing' | 'success' | 'error'
}
export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  files?: Array<{ url: string; fileName: string; type: 'xlsx' | 'pdf' }>
  toolCalls?: MessageToolCall[]
  canSendAsEmail?: boolean
  emailSent?: boolean
  emailSentAt?: Date
}

// Типы для API ответов
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface SessionsResponse {
  sessions: ChatSession[]
}

export interface MessagesResponse {
  messages: ChatMessage[]
}

// Типы для состояния загрузки
export interface LoadingState {
  isLoading: boolean
  error: string | null
}

// Типы для валидации
export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

// Типы для метрик производительности
export interface PerformanceMetrics {
  [key: string]: {
    average: number
    count: number
  }
}
