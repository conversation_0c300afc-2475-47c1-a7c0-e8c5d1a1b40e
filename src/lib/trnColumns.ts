export function trnColumns(col: string) {
  const trns = {
    prod_sku: 'Артикул',
    prod_analogsku: 'OEM',
    prod_analogs: 'Аналоги',
    prod_manuf: 'Производитель',
    prod_purpose: 'Назначение',
    prod_cat: 'ID категории',
    prod_morecats: 'Прочие категории',
    prod_price: 'Цена розн.',
    prod_year: 'Год',
    prod_model: 'Модель',
    prod_type: 'Тип',
    prod_id: 'ID',
    prod_count: 'Наличие',
    prod_note: 'Примечание',
    prod_composition: 'Заметки',
    prod_uses: 'Применение',
    prod_size: 'Размер',
    prod_discount: 'Скидка %',
    prod_purchasing: 'Закуп',
    prod_material: 'Материал',
    prod_weight: 'Вес',
    prod_group: 'Группа',
    prod_group_count: 'Наличие по группе',
    prod_group_price: 'Цена по группе',
    prod_rk: 'Состав РК',
    prod_minalert: 'Мин.кол-во для оповещ.',
    prod_img: 'Изобр.',
    prod_img_rumi: 'Изобр.rumi',
    prod_secret: 'Секрет',
    prod_coeff: 'Коэф',
    prod_cell: 'Место',
    created_at: 'Создан',
    whosaleprice: 'Цена опт',
    prod_place: 'Место',
    prod_supplier: 'Поставщик',
    orderCount: 'Кол-во в заказе',
    item_count: 'Кол-во в заказе',
    _qty: 'Кол-во в заказе',
    client_id: 'ID',
    client_name: 'Имя',
    client_mail: 'E-mail',
    client_number: 'Номер',
    client_phone: 'Телефон',
    client_country: 'Страна',
    client_adress: '--',
    client_city: 'Город',
    client_street: 'Улица',
    client_house: 'Дом',
    client_flat: 'Квартира/Офис',
    client_postindex: 'Почтовый индекс',
    updated_at: 'Дата обновления',
    client_password: '--',
    remember_me_token: '--',
    org_adress: 'Юр. Адрес',
    org_bank: 'Банк',
    org_bik: 'БИК',
    org_inn: 'ИНН',
    org_kpp: 'КПП',
    org_kschet: 'Кор.счет',
    org_name: 'Наименование юр.лица',
    org_rschet: 'Рас.счет',
    org_vat: 'VAT',
    order_status: 'Статус',
    order_shipping: 'Доставка',
    order_payment: 'Оплата',
    order_price: 'Сумма заказа',
    order_shippingprice: 'Стоимость доставки',
    order_desc: 'Пометки',
    order_clienttype: 'Тип клиента',
    order_notice: 'Примечание',
    order_gtd: 'Вывоз',
    order_tracknumber: 'Трек-номер',
    'client_discount': 'Персональная скидка',
    'client_cdekid': 'СДЭК ID',
    size_h_2: 'Высота/2',
    images: 'Доп.изобр'
  }

  return trns[col] || col
}
