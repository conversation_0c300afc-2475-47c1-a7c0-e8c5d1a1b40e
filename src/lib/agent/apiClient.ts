import { MastraClient } from '@mastra/client-js'
import type {
  AgentRequest,
  AgentStreamRequest,
  AgentResponse,
  ChatSession,
  ChatMessage,
  StreamChunk,
  ToolCall
} from '@/lib/interfaces/Agent'



class AgentApiClient {
  private mastraClient: MastraClient
  private baseUrl: string

  constructor() {
    // Для Mastra используем прямой URL без прокси
    this.baseUrl = import.meta.env.DEV
      ? 'http://localhost:4111'
      : import.meta.env.VITE_MASTRA_URL || 'http://localhost:4111'

    // Инициализируем Mastra client
    this.mastraClient = new MastraClient({
      baseUrl: this.baseUrl
    })
  }

  // Отправка сообщения агенту через Mastra JS Client
  async sendMessage(request: AgentRequest): Promise<AgentResponse> {
    try {
      const agent = this.mastraClient.getAgent('orgManagerAgent')

      const response = await agent.generate({
        messages: [
          {
            role: 'user',
            content: request.message
          }
        ],
        threadId: request.threadId,
        resourceId: 'user-default',
        maxSteps: 15,
      })

      // Логируем что возвращает Mastra
      //console.log('Mastra response:', response)
      //console.log('Response text:', response.text)
      //console.log('Response text type:', typeof response.text)

      // Преобразуем toolCalls в нужный формат
      const toolCalls: ToolCall[] = response.toolCalls?.map(toolCall => {
        const generatedId = `tool-${Date.now()}-${Math.random()}`
        return {
          displayId: `${generatedId}-history`,
          toolCallId: generatedId,
          toolName: toolCall.toolName,
          input: toolCall.args,
          output: undefined, // Будет заполнено после выполнения
          status: 'success'
        }
      }) || []

      // Убеждаемся, что message - это строка
      let messageText = ''
      if (typeof response.text === 'string') {
        messageText = response.text
      } else if (response.text && typeof response.text === 'object') {
        console.error('RESPONSE.TEXT IS OBJECT:', response.text)
        messageText = JSON.stringify(response.text, null, 2)
      } else {
        console.error('UNKNOWN RESPONSE FORMAT:', response)
        messageText = String(response.text || 'Ошибка: неизвестный формат ответа')
      }

      return {
        message: messageText,
        threadId: request.threadId || `thread-${Date.now()}`,
        toolCalls: toolCalls,
        files: [] // Пока не поддерживается в текущем API
      }
    } catch (error) {
      console.error('Ошибка отправки сообщения через Mastra client:', error)
      throw new Error(`Mastra client error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Стриминг сообщений через Mastra JS Client
  async *streamMessage(request: AgentStreamRequest): AsyncGenerator<StreamChunk> {
    try {
      const agent = this.mastraClient.getAgent('orgManagerAgent')

      const response = await agent.stream({
        messages: [
          {
            role: 'user',
            content: request.message
          }
        ],
        threadId: request.threadId,
        resourceId: 'user-default'
      })

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Response body is null')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (let line of lines) {
            if (line.trim() === '') continue

            // Убираем префикс SSE "data:" если он присутствует
            if (line.startsWith('data:')) {
              line = line.substring(5).trimStart()
            }

            if (line.startsWith('0:')) {
              try {
                const content = JSON.parse(line.substring(2))
                if (typeof content === 'string') {
                  yield { type: 'text', content }
                }
              } catch (e) {
                console.error('Failed to parse stream text chunk:', line, e)
              }
            } else if (line.startsWith('4:')) { // Дополнительный текстовый чанк
              try {
                const data = JSON.parse(line.substring(2))
                let textContent = ''
                if (typeof data === 'string') {
                  textContent = data
                } else if (data && typeof data === 'object' && typeof (data as any).text === 'string') {
                  textContent = (data as any).text
                }
                if (textContent) {
                  yield { type: 'text', content: textContent }
                }
              } catch (e) {
                console.error('Failed to parse stream text chunk (4:)', line, e)
              }
            } else if (line.startsWith('9:')) { // Новый префикс tool-start
              try {
                const toolData = JSON.parse(line.substring(2));
                if (toolData && toolData.toolCallId) {
                  yield {
                    type: 'tool-start',
                    toolCall: {
                      displayId: `${toolData.toolCallId}-input`,
                      toolCallId: toolData.toolCallId,
                      toolName: toolData.toolName || 'unknownTool',
                      input: toolData.args || toolData.input,
                      status: 'executing'
                    }
                  }
                }
              } catch (e) {
                console.error('Failed to parse tool-start chunk (9:)', line, e)
              }
            } else if (line.startsWith('a:')) { // Новый префикс tool-output
              try {
                const toolData = JSON.parse(line.substring(2));
                if (toolData && toolData.toolCallId) {
                  // В некоторых случаях результат находится в field result.result
                  const output = toolData.output || toolData.result || (toolData.result && toolData.result.result)
                  yield {
                    type: 'tool-output',
                    toolCall: {
                      toolCallId: toolData.toolCallId,
                      output: output
                    }
                  }
                }
              } catch (e) {
                console.error('Failed to parse tool-output chunk (a:)', line, e)
              }
            } else if (line.startsWith('3:')) { // Ошибка
              try {
                const errorContent = JSON.parse(line.substring(2))
                if (typeof errorContent === 'string') {
                  yield { type: 'error', error: errorContent }
                }
              } catch (e) {
                console.error('Failed to parse stream error chunk:', line, e)
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }
    } catch (error) {
      console.error('Ошибка стриминга через Mastra client:', error)
      yield { type: 'error', error: `Mastra client streaming error: ${error instanceof Error ? error.message : 'Unknown error'}` }
    }
  }

  // Получение списка сессий через Mastra JS Client Memory API
  async getSessions(): Promise<ChatSession[]> {
    try {
      const resourceId = 'user-default'
      const agentId = 'orgManagerAgent'

      const threads = await this.mastraClient.getMemoryThreads({
        resourceId: resourceId,
        agentId: agentId
      })

      return threads.map((thread: any) => ({
        id: thread.id,
        title: thread.metadata?.title || `Чат ${thread.id.slice(-8)}`,
        createdAt: thread.createdAt || new Date().toISOString(),
        updatedAt: thread.updatedAt || new Date().toISOString(),
        messageCount: thread.messageCount || 0,
        lastMessage: thread.lastMessage || '',
        metadata: thread.metadata
      }))
    } catch (error) {
      console.error('Ошибка получения сессий через Mastra client:', error)
      return []
    }
  }

  // Создание новой сессии через Mastra JS Client Memory API
  async createSession(): Promise<ChatSession> {
    try {
      const resourceId = 'user-default'
      const agentId = 'orgManagerAgent'

      const thread = await this.mastraClient.createMemoryThread({
        title: 'Новый чат',
        metadata: { category: 'support', type: 'orgManagerAgent_chat' },
        resourceId: resourceId,
        agentId: agentId
      })

      return {
        id: thread.id,
        title: (thread.metadata as any)?.title || 'Новый чат',
        createdAt: thread.createdAt instanceof Date ? thread.createdAt.toISOString() : (thread.createdAt as any) || new Date().toISOString(),
        updatedAt: thread.updatedAt instanceof Date ? thread.updatedAt.toISOString() : (thread.updatedAt as any) || new Date().toISOString(),
        messageCount: 0,
        metadata: thread.metadata as any
      }
    } catch (error) {
      console.error('Ошибка создания сессии через Mastra client:', error)
      const sessionId = `thread-${Date.now()}`
      return {
        id: sessionId,
        title: 'Новый чат',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        messageCount: 0
      }
    }
  }

  // Удаление сессии через Mastra JS Client Memory API
  async deleteSession(sessionId: string): Promise<void> {
    try {
      const agentId = 'orgManagerAgent'
      const thread = this.mastraClient.getMemoryThread(sessionId, agentId)
      await thread.delete()
    } catch (error) {
      console.error('Ошибка удаления сессии через Mastra client:', error)
    }
  }

  // Получение истории сообщений через Mastra JS Client Memory API
  async getMessages(threadId: string): Promise<ChatMessage[]> {
    try {
      const agentId = 'orgManagerAgent'
      const thread = this.mastraClient.getMemoryThread(threadId, agentId)
      const { messages } = await thread.getMessages({ limit: 100 })

      return messages.map((msg: any) => {
        let contentText = ''
        // Если content - это массив (структурированный ответ), извлекаем текст
        if (Array.isArray(msg.content)) {
            const textPart = msg.content.find((part: {type: string}) => part.type === 'text')
            if(textPart) contentText = (textPart as any).text
        } else if (typeof msg.content === 'string') {
            contentText = msg.content
        } else if (typeof msg.text === 'string') { // Fallback for older format
            contentText = msg.text
        }
        
        return {
        id: msg.id || `msg-${Date.now()}`,
        threadId: threadId,
        role: msg.role || 'user',
        content: contentText,
        timestamp: msg.timestamp || new Date().toISOString(),
        attachments: msg.attachments || [],
        files: msg.files || [],
        toolCalls: msg.toolCalls || (Array.isArray(msg.content) ? msg.content.filter((p: any) => p.type !== 'text') : []),
        metadata: msg.metadata
        }
      })
    } catch (error) {
      console.error('Ошибка получения истории сообщений через Mastra client:', error)
      return []
    }
  }
}

// Экспортируем единственный экземпляр клиента
export const agentApiClient = new AgentApiClient()
