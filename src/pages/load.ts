import { loadProblemOrders, loadCounters, loadSMSru, loadZadarmaBalance, loadZadarmaStats, loadDebtors } from './HomePage.vue';

// watch(isLoading, () => //console.log('isLoading:', isLoading))
export async function load() {
const res = await Promise.all([
loadProblemOrders(),
loadCounters(),
loadSMSru(),
loadZadarmaBalance(),
loadZadarmaStats(),
// loadOrderProccesingData(),
// loadTopProducts(),
loadDebtors(),
// loadExCounters(),
loadZadarmaBalance(),
loadSMSru()
]);

//console.log('res:', res);

return res;
}
