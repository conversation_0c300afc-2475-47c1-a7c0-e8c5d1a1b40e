<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import { useQuery } from '@tanstack/vue-query'
import { trpc } from '@/tRPC'
import InputText from 'primevue/inputtext'
import <PERSON>ton from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'

const router = useRouter()
const toast = useToast()

type HtmlChunk = {
  id: number
  keyname: string
  title: string
  body?: string
}

const key = ref('')
const id = ref<string>('')

function openEditor(q?: { id?: number; key?: string }) {
  const query: Record<string, string> = {}
  if (q?.id) query.id = String(q.id)
  if (q?.key) query.key = q.key
  if (!q && id.value) query.id = String(Number(id.value))
  if (!q && key.value) query.key = key.value
  if (!query.id && !query.key) {
    toast.add({ severity: 'warn', summary: 'Укажите id или key', life: 2500 })
    return
  }
  router.push({ path: '/settings/chunk', query })
}

const search = ref('')
const { isLoading, isFetching, data: chunks, refetch } = useQuery<HtmlChunk[]>({
  queryKey: ['getHtmlChunks'],
  queryFn: async () => await (trpc as any).services.getHtmlChunks.query(),
  refetchOnWindowFocus: false
})

const filtered = computed(() => {
  const s = search.value.trim().toLowerCase()
  if (!s) return chunks?.value || []
  return (chunks?.value || []).filter(
    (c) => String(c.id).includes(s) || c.keyname?.toLowerCase().includes(s) || c.title?.toLowerCase().includes(s)
  )
})
</script>

<template>
  <div class="space-y-5">
    <h1 class="text-xl font-semibold">Настройки</h1>

    <div class="bcard p-4 space-y-4">
      <h2 class="text-base font-medium">HTML-чанки</h2>
      <div class="flex items-center justify-between gap-3">
        <span class="flex items-center gap-2">
          <InputText v-model="search" placeholder="Поиск по id/key/title" />
          <Button icon="pi pi-replay" text :loading="isFetching || isLoading" @click="() => refetch()" />
        </span>
        <div class="hidden md:flex items-end gap-2">
          <InputText v-model="id" inputmode="numeric" placeholder="ID (опционально)" />
          <InputText v-model="key" placeholder="Key (опционально)" />
          <Button label="Открыть редактор" icon="pi pi-external-link" @click="() => openEditor()" />
        </div>
      </div>

      <DataTable :value="filtered" :loading="isLoading" table-class="table-default" stripedRows :rows="20" :rowsPerPageOptions="[20,50,100]" paginator scrollable scrollHeight="60vh">
        <Column field="id" header="ID" sortable style="width:8rem" />
        <Column field="keyname" header="Key" sortable />
        <Column field="title" header="Заголовок" sortable />
        <Column header="">
          <template #body="{ data }">
            <Button size="small" icon="pi pi-pencil" label="Редактировать" @click="() => openEditor({ id: data.id, key: data.keyname })" />
          </template>
        </Column>
      </DataTable>

      <div class="md:hidden flex items-end gap-2">
        <InputText v-model="id" inputmode="numeric" placeholder="ID (опционально)" />
        <InputText v-model="key" placeholder="Key (опционально)" />
        <Button label="Открыть" icon="pi pi-external-link" @click="() => openEditor()" />
      </div>
    </div>
  </div>
</template>
