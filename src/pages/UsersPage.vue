<script lang="ts" setup>
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import Button from 'primevue/button'
import Column from 'primevue/column'
import DataTable from 'primevue/datatable'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import Dialog from 'primevue/dialog'
import { ref } from 'vue'
import Password from 'primevue/password'
import InputText from 'primevue/inputtext'
import Dropdown from 'primevue/dropdown'

const confirm = useConfirm()
const toast = useToast()

const updateModalShow = ref(false)
const currentUser = ref({})
const newPassword = ref('')
const newRole = ref('')

const { isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryKey: ['productsWithoutPhoto'],
  queryFn: async () => await loadData(),
  refetchOnWindowFocus: false,
  refetchOnMount: true
})

async function loadData() {
  const res = await api('/cpan/users/list')
  return res.body
}

function deleteHandler(event, item) {
  confirm.require({
    target: event.currentTarget,
    message: 'Подтвердит удаление',
    icon: 'pi pi-exclamation-triangle',
    accept: async () => {
      const res = await api('/cpan/users/delete?id=' + item.id)
      if (res.ok) {
        toast.add({ severity: 'success', summary: 'Юзер удален', life: 3000 })
        refetch()
      }
    },
    reject: () => {
      toast.add({ severity: 'error', summary: 'Ошибка', detail: '', life: 3000 })
    }
  })
}

function updateHandler(userData) {
  currentUser.value = userData
  newRole.value = userData.user_role
  updateModalShow.value = true
}

async function mutateUser() {
  const res = await api('/cpan/users/update', {
    data: {
      payload: {
        ...currentUser.value,
        password: newPassword.value,
        user_role: newRole.value
      }
    }
  })
  if (res.ok) {
    toast.add({ severity: 'success', summary: 'Юзер обновлен', life: 3000 })
    refetch()
  } else {
    toast.add({ severity: 'error', summary: 'Ошибка', detail: '', life: 3000 })
  }
}
</script>

<template>
  <span class="text-slate-600 dark:text-zinc-100 font-semibold">Пользователи</span>

  <!-- <ul>
    <li :key="item.id" v-for="(item, index) in data?.data">
      <a class="hover:underline" :href="'/goods?searchvalue=' + item.oem" target="_blank">
        <span class="font-semibold">{{ index + 1 }}.</span> <span> {{ item.oem }} / {{ item.code }}</span>
      </a>
    </li>
  </ul> -->

  <div class="container mx-auto mt-5">
    <DataTable table-class="table-default"  :loading="isFetching" size="small" showGridlines :value="data" tableStyle="min-width: 50rem">
      <template #header>
        <div class="text-sm">
          <span class="bg-yellow-300 text-slate-700 p-1 px-2 rounded-lg">SU</span>
          - суперюзер
        </div>
      </template>
      <Column style="width: 80px; text-align: center" field="user_id" header="ID"></Column>
      <Column style="width: 140px; text-align: center" field="user_name" header="Логин"></Column>
      <Column style="width: 80px; text-align: center" field="user_role" header="Роль">
        <!-- <template #body="{ data, field }">
          <Dropdown v-model="data[field]" :options="['su', 'admin']" _optionLabel="name" placeholder="Выбрать роль" class="w-full md:w-14rem" />
        </template> -->
      </Column>
      <Column style="width: 40px; text-align: center" field="action" header="">
        <template #body="{ data }">
          <Button @click="($event) => updateHandler(data)" rounded icon="pi pi-pencil" label="" size="small" severity="primary" text />
          <Button @click="($event) => deleteHandler($event, data)" rounded label="X" size="small" severity="danger" outlined text />
        </template>
      </Column>
    </DataTable>
  </div>

  <Dialog dismissableMask v-model:visible="updateModalShow" modal header="Обновить" :style="{ width: '50rem' }">
    <div class="flex flex-col space-y-3">
      <div>
        <div>Новый пароль</div>
        <div>
          <!-- <Password v-model="currentUser.user_password" toggleMask /> -->
          <InputText type="text" v-model="newPassword" />
          <!-- currentUser: {{ currentUser }} password: {{ newPassword }} -->
        </div>
      </div>
      <div>
        <div>Роль</div>
        <div>
          <Dropdown v-model="newRole" :options="['su', 'admin']" _optionLabel="name" placeholder="Выбрать роль" />
        </div>
      </div>
      <div class="text-slate-400">
        Не доступно
        <hr />
      </div>
      <div>
        <div class="text-slate-400">Телефон</div>
        <div>
          <!-- <Password v-model="currentUser.user_password" toggleMask /> -->
          <InputText disabled type="text" v-model="currentUser.user_phone" />
        </div>
      </div>
      <div>
        <div class="text-slate-400">Email</div>
        <div>
          <!-- <Password v-model="currentUser.user_password" toggleMask /> -->
          <InputText disabled type="text" v-model="currentUser.user_mail" />
        </div>
      </div>
    </div>
    <div class="flex justify-end">
      <Button @click="mutateUser" label="Сохранить" icon="pi pi-save" text />
    </div>
  </Dialog>
</template>
