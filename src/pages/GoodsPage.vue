<script setup lang="ts">
import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { useRoute, useRouter } from 'vue-router'
import { api } from '@/lib/_api'

import { computed, onBeforeMount, onMounted, onUpdated, ref, watch } from 'vue'

import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'

import { trnColumns } from '@/lib/trnColumns'
// import ProductCard from './ProductCard.vue'

// import Paginator from 'primevue/paginator'
import InputNumber from 'primevue/inputnumber'
import Dropdown from 'primevue/dropdown'
// import ProgressBar from 'primevue/progressbar'
// import Avatar from 'primevue/avatar'
import Image from 'primevue/image'
import DataTable from 'primevue/datatable'
import ContextMenu from 'primevue/contextmenu'
import MultiSelect from 'primevue/multiselect'
import InputText from 'primevue/inputtext'
import Button from 'primevue/button'
import ColumnGroup from 'primevue/columngroup'
import Column from 'primevue/column'

import Dialog from 'primevue/dialog'
import ProductCard from '@/components/product/ProductCard.vue'

const route = useRoute()
const router = useRouter()

const confirm = useConfirm()
const toast = useToast()

const queryClient = useQueryClient()

let URL = new URLSearchParams(window.location.search)

if (!URL.has('limit')) {
  URL.set('limit', '20')
}

const loading = ref(false),
  totalRecords = ref(0),
  currentPage = ref(1),
  pageSize = ref(10),
  products = ref(),
  selectedProducts = ref([]),
  createNewModal = ref(false),
  globalSearchValue = ref(),
  selectedProduct = ref(),
  copySelectedProduct = ref(),
  showProductCard = ref(),
  filters = ref({}),
  lazyParams = ref({}),
  showSl = ref(true),
  columns = ref([]),
  editingRows = ref([]),
  lockedRows = ref([]),
  cm = ref(),
  dt = ref(),
  rowsGroupBy = ref(),
  showColumns = ref(['prod_purpose', 'prod_sku', 'prod_analogsku', 'prod_manuf', 'prod_type', 'prod_material', 'prod_size']),
  menuModel = ref([
    { label: 'Открыть', icon: 'pi pi-fw pi-search', command: () => viewProduct(selectedProduct.value) },
    { label: 'Открыть в новом окне', icon: 'pi pi-fw pi-search', command: () => window.open('/products/' + selectedProduct.value.prod_id, '_blank').focus() },
    { label: 'Удалить', icon: 'pi pi-fw pi-times', command: () => deleteProduct(selectedProduct.value) }
  ]),
  productsActions = ref([
    {
      label: 'Удалить',
      value: 'delete'
    },
    {
      label: 'Скрыть',
      value: 'hide'
    }
  ]),
  productsCurrentAction = ref(),
  sortField = ref(''),
  sortOrder = ref(0)

const { isLoading, isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryKey: ['products', route.fullPath],
  queryFn: getProducts,
  onError: (e) => apiErrorToast(e),
  refetchOnWindowFocus: false,
  onSuccess: (data) => {
    columns.value = data.columns?.map((c) => ({
      field: c,
      header: trnColumns(c)
    }))

    products.value = data.products?.data
    totalRecords.value = data.products?.meta.total

    pageSize.value = data.products?.meta.per_page
    currentPage.value = data.products?.meta.current_page
    loading.value = false

    if (URL.has('filters')) {
      try {
        let _filters = JSON.parse(URL.get('filters') || '')
        Object.keys(_filters).map((key) => (filters.value[key] = _filters[key]))
      } catch (error) {
        console.warn(error)
      }
    }
  }
})

async function getProducts() {
  // console.log('get products: ', {
  //   windse: window.location.search,
  //   URL: URL.toString(),
  //   path: route.fullPath
  // })

  const res = await api('/cpan/products?' + URL.toString())
  if (res.ok) {
    return res.body
  } else {
    throw new Error(`${res.status}: ${res.statusText}: ${res.message}`)
  }
}

async function loadLazyData() {
  // refetch()
  queryClient.invalidateQueries()
}

function onRowContextMenu(event) {
  cm.value.show(event.originalEvent)
}

function viewProduct(product) {
  showProductCard.value = true
  //toast.add({severity: 'info', summary: 'Product Selected', detail: product.name})
}

function deleteProduct(product) {
  confirm.require({
    message: `Удалить ${product.prod_purpose} ${product.prod_analogsku} ?`,
    header: 'Подтвердить действие',
    icon: 'pi pi-exclamation-triangle',
    acceptLabel: 'Удалить',
    rejectLabel: 'Отмена',
    accept: async () => {
      try {
        const res = await api('/cpan/products/delete/' + product.prod_id)
        loadLazyData()

        res.ok ? toast.add({ severity: 'success', summary: 'Товар удален', detail: '', life: 3000 }) : apiErrorToast(res)
      } catch (error) {
        apiErrorToast({ status: 'browser', statusText: error })
      }
    },
    reject: () => {}
  })
  selectedProduct.value = null
}

function apiErrorToast(res) {
  toast.add({ severity: 'error', summary: `Ошибка cервера`, detail: `${res.status}, ${res.statusText}, ${res.message || res}`, life: 6000 })
}

async function saveCell(e) {
  let editProduct = products.value.find((x) => x.prod_id == e.data.prod_id)

  if (!editProduct) {
    toast.add({ severity: 'error', summary: 'Ошибка', detail: `Не найден товар для обновления. id: ${e.data.prod_id}, oem: ${e.data.prod_analogsku}`, life: 4000 })
    return
  }

  if (editProduct.editFields) {
    let payloads = {
      fields: editProduct.editFields,
      prod_id: editProduct.prod_id
    }

    await saveProduct(payloads)
  } else {
    toast.add({ severity: 'info', summary: 'Нет изменений', detail: `для обновления поля "${trnColumns(e.field)}"`, life: 4000 })
  }
}

async function saveProduct(payloads) {
  try {
    const res = await api('/cpan/products/update/', { data: payloads, method: 'POST' })
    res.ok ? toast.add({ severity: 'success', summary: 'Данные обновлены', detail: '', life: 3000 }) : apiErrorToast(res)
    routerPush()
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Ошибка cервера', detail: error + ` ${error.message || ''}`, life: 6000 })
  }
}

function onCellEdit(newValue, props) {
  if (!products.value[props.index].editFields) {
    products.value[props.index].editFields = {}
  }

  products.value[props.index].editFields[props.column.props.field] = newValue

  if (!products.value[props.index]) {
    products.value[props.index] = { ...props.data }
  }
}

const onRowEditSave = async (event) => {
  let { data, newData, index } = event
  let fields = {}

  Object.keys(newData).map((key) => (newData[key] != data[key] ? (fields[key] = newData[key]) : false))

  let payloads = {
    fields,
    prod_id: newData.prod_id
  }

  await saveProduct(payloads)
  // URL.set('reload', String(Math.random()))
  routerPush()
  // router.push({ query: getQueryParams('fromOnRowEditSave') })
}

function onPage(event) {
  URL.set('page', event.page + 1)
  URL.set('limit', event.rows)
  routerPush()

  // router.push({ query: getQueryParams('fromOnPage') })
}

function onSort(event) {
  URL.set('sortField', event.sortField)
  URL.set('sortOrder', event.sortOrder == -1 ? 'asc' : 'desc')
  routerPush()

  // router.push({ query: Object.fromEntries(URL) })
}

function onFilter(event) {
  let _filters = {}
  Object.keys(filters.value).map((key) => (filters.value[key].value ? (_filters[key] = filters.value[key]) : ''))

  URL.set('filters', JSON.stringify(_filters))

  routerPush()
  // router.push({ query: getQueryParams('fromOnFilter') })
}

function routerPush() {
  loadLazyData()
  router.push({ query: Object.fromEntries(URL) })
}

const duplicateProduct = () => {
  copySelectedProduct.value.prod_purpose = 'Копия ' + copySelectedProduct.value.prod_purpose
}

const exportCSV = () => {
  dt.value.exportCSV()
}

const activeColumns = computed(() => showColumns.value.map((i) => ({ field: i, header: trnColumns(i) })))

const toggleLock = (data, frozen, index) => {
  if (frozen) {
    lockedRows.value = lockedRows.value.filter((c, i) => i !== index)
    products.value.push(data)
  } else {
    products.value = products.value.filter((c, i) => i !== index)
    lockedRows.value.push(data)
  }

  products.value.sort((val1, val2) => {
    return val1.id < val2.id ? -1 : 1
  })
}

watch(selectedProducts, () => console.log('selectedProducts: ', selectedProducts.value))
watch(selectedProduct, () => (copySelectedProduct.value = JSON.parse(JSON.stringify(selectedProduct.value))))

watch(currentPage, () => {
  URL.set('page', currentPage.value.toString())
  routerPush()
})

watch(rowsGroupBy, () => {
  URL.set('rowsgroupby', rowsGroupBy.value || '')
  routerPush()
  // window.history.replaceState({}, route.fullPath, route.path + '/' + URL.toString())
})

watch(showColumns, () => {
  if (showColumns.value) {
    let val = String(showColumns.value.join(','))
    URL.set('showcolumns', val)
    routerPush()
    // window.history.replaceState({}, route.fullPath, route.path + '/?' + URL.toString())
  }
})
watch(showColumns, () => showColumns.value.map((col) => (filters.value[col] = { value: '', matchMode: 'contains' })))

watch(filters, () => console.log('filters:,', filters.value))

async function startSearch(e) {
  URL.set('searchvalue', globalSearchValue.value) //
  routerPush()
}

onBeforeMount(() => {
  showColumns.value.map((col) => (filters.value[col] = { value: '', matchMode: 'contains' }))
})

onMounted(() => {
  // URL = new URLSearchParams(window.location.search)
  // //console.log('@@onMonted')
  try {
    document.title = 'Товары'
  } catch (error) {}
})
</script>

<template>
  <div>
    <!-- <ProgressBar class="h-2" v-if="isLoading || isFetching" mode="indeterminate" /> -->

    <div v-if="isSuccess" style="height: calc(100vh - 143px)">
      <ContextMenu :model="menuModel" ref="cm" />
      <DataTable
        table-class="table-default"
        exportable
        :unstyled="false"
        size="small"
        :value="products"
        :lazy="true"
        :paginator="true"
        :rowsPerPageOptions="[10, 20, 50, 100, 200, 500, 1000]"
        :rows="pageSize"
        :first="Math.ceil((currentPage - 1) * pageSize)"
        :totalRecords="totalRecords"
        :sortField="sortField"
        :sortOrder="sortOrder"
        alwaysShowPaginator
        paginatorTemplate="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
        currentPageReportTemplate="Товары {first} - {last} из {totalRecords}"
        v-model:filters="filters"
        ref="dt"
        :loading="isLoading || isFetching"
        v-model:selection="selectedProducts"
        v-model:editingRows="editingRows"
        dataKey="prod_id"
        @page="onPage($event)"
        @sort="onSort($event)"
        @filter="onFilter($event)"
        @row-edit-save="onRowEditSave"
        filterDisplay="row"
        style="font-size: 17px; padding: 0"
        :globalFilterFields="['name', 'country.name', 'company', 'representative.name']"
        stripedRows
        :resizableColumns="true"
        showGridlines
        responsiveLayout="stack"
        breakpoint="900px"
        stateStorage="local"
        columnResizeMode="fit"
        stateKey="dt-products-state"
        reorderableColumns
        editMode="row"
        contextMenu
        v-model:contextMenuSelection="selectedProduct"
        @rowContextmenu="onRowContextMenu"
        _rowGroupMode="subheader"
        :groupRowsBy="rowsGroupBy"
        scrollable
        scrollHeight="flex"
        _frozenValue="lockedRows"
      >
        <template #header>
          <div class="space-x-3 flex justify-between">
            <div>
              <MultiSelect class="w-60" v-model="showColumns" :options="columns" optionValue="field" optionLabel="header" placeholder="Столбцы" />
            </div>
            <div>
              <InputText @keyup.enter="startSearch" v-model.lazy="globalSearchValue" class="h-12 rounded-r-none" placeholder="Поиск" />
              <Button @click="startSearch" icon="pi pi-search" iconPos="right" class="h-12 rounded-l-none" label="" />
            </div>
            <div>
              <Button class="p-button-text text-lg" iconPos="right" icon="pi pi-plus-circle" @click="createNewModal = true" label="Создать" />
            </div>
          </div>
        </template>
        <ColumnGroup style="max-width: 40px" v-if="showSl" selectionMode="multiple" headerStyle="width: 3em"> </ColumnGroup>
        <template v-if="rowsGroupBy" #groupheader="slotProps">
          <span
            >Группа по <b>{{ trnColumns(rowsGroupBy) }}</b
            >: {{ slotProps.data[rowsGroupBy] }}
          </span>
        </template>
        <Column :sortable="true" field="prod_id" header="ID" style="max-width: 80px" bodyStyle="text-align:center">
          <template #body="{ data }">
            <router-link class="hover:underline" :to="'/products/' + data.prod_id">{{ data.prod_id }}</router-link>
          </template>
        </Column>

        <Column v-for="(col, index) in activeColumns" :key="index" :field="col.field" :header="col.header" filterMatchMode="startsWith" :ref="col.field" :sortable="true">
          <template #editor="{ data, field }">
            <InputText v-model="data[field]" />
          </template>

          <template #body="{ data, field }">
            <router-link v-if="field == 'prod_purpose'" class="hover:underline" :to="'/products/' + data.prod_id"> {{ data[field] }}</router-link>
            <span v-else> {{ data[field] }} </span>
          </template>

          <template #filter="{ filterModel, filterCallback }">
            <InputText
              :pt="{
                root: 'w-20'
              }"
              :ptOptions="{ mergeProps: true }"
              size="small"
              type="text"
              v-model="filterModel.value"
              @keydown.enter="filterCallback()"
              placeholder="Поиск по"
            />
          </template>
        </Column>

        <Column style="max-width: 140px" bodyStyle="text-align:center" field="prod_count" header="Наличие">
          <template #editor="{ data, field }">
            <InputNumber
              :min="0"
              v-model="data[field]"
              autofocus
              inputClass="w-20"
              showButtons
              bbuttonLayout="vertical"
              decrementButtonClass="p-button-secondary"
              incrementButtonClass="p-button-secondary"
              incrementButtonIcon="pi pi-plus"
              decrementButtonIcon="pi pi-minus"
            />
          </template>
        </Column>

        <Column style="max-width: 140px" bodyStyle="text-align:center" header="Фото">
          <template #body="{ data, field }">
            <!-- <Avatar shape="circle" size="large" :image="'https://mirsalnikov.ru/data/rti/' + data.prod_img + '.jpg'" /> -->
            <div class="rounded-lg">
              <Image image-class="rounded-lg" class="rounded-lg" :src="'https://mirsalnikov.ru/data/rti/' + data.prod_img + '.jpg'" alt="Image" width="50" preview />
            </div>
          </template>
          <template #editor="{ data, field }">
            <InputText type="text" v-model="data.prod_img" placeholder="Фото" />
          </template>
        </Column>

        <Column :rowEditor="true" frozen alignFrozen="right" style="max-width: 80px" bodyStyle="text-align:center"> </Column>

        <template #paginatorstart>
          <div class="flex space-x-3 items-center">
            <Button @click="loadLazyData" type="button" icon="pi pi-refresh" class="p-button-text" />
            <Button class="p-button-text" iconPos="right" icon="pi pi-external-link" label="Выгрузить" @click="exportCSV($event)" />
            <div>
              <span>Группировать по: </span>
              <Dropdown v-model="rowsGroupBy" :options="columns" placeholder="Группировка" optionValue="field" optionLabel="header" :showClear="true">
                <template #option="slotProps">
                  <div>
                    <span>{{ slotProps.option.header }}</span>
                  </div>
                </template>
              </Dropdown>
            </div>
          </div>
        </template>
      </DataTable>
      <div v-if="selectedProducts?.length">
        <Dropdown v-model="productsCurrentAction" :options="productsActions" optionLabel="label" optionValue="value" placeholder="С выбранными" />
        <Button disabled="disabled" icon="pi pi-check" label="Применить" class="p-button-secondary" />
      </div>
    </div>
  </div>

  <div v-if="showProductCard">
    <Dialog
      dismissableMask
      modal
      :breakpoints="{ '960px': '80vw' }"
      maximizable
      class="w-4/5"
      :header="copySelectedProduct.prod_purpose + ' ' + copySelectedProduct.prod_analogsku"
      v-model:visible="showProductCard"
    >
      <ProductCard @update="loadLazyData" :productData="copySelectedProduct || {}"></ProductCard>
    </Dialog>
  </div>

  <div v-if="createNewModal">
    <Dialog :dismissableMask="false" modal header="Новый товар" v-model:visible="createNewModal" class="w-4/5 md:w-11/12">
      <ProductCard @update="loadLazyData" @duplicate="duplicateProduct" :productData="{}"></ProductCard>
    </Dialog>
  </div>
</template>
