<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { api } from '@/lib/_api'

import TotalSalesChart from '@/components/charts/TotalSalesChart.vue'
import OrdersChart from '@/components/charts/OrdersChart.vue'
import CategoriesChart from '@/components/charts/CategoriesChart.vue'
import CountersBlock from '@/components/CountersBlock.vue'
import CanceledCompare from '@/components/charts/CanceledCompare.vue'
import ZadarmaCallList from '@/components/ZadarmaCallList.vue'
import TopProducts from '@/components/TopProducts.vue'
import OrderProccesingTime from '@/components/charts/OrderProccesingTime.vue'

import Journal from '@/components/Journal.vue'
import Skeleton from 'primevue/skeleton'
import ScrollPanel from 'primevue/scrollpanel'

import InputText from 'primevue/inputtext'
import * as dayjs from 'dayjs'
import ProgressSpinner from 'primevue/progressspinner'
import { nextTick } from 'vue'
import { onBeforeMount } from 'vue'
import { useQuery } from '@tanstack/vue-query'

interface TopSale {
  prod_id: number
  prod_sku: string
  prod_analogsku: string
  prod_cat: string
  prod_count: number
  total_sales: number
}

interface TopProductsType {
  topSales: TopSale[]
  topRequest: TopRequest[]
}

interface TopRequest {
  query: string
  total_count: number
}

const shippingEnums = {
  ems: 'EMS',
  'pochta': 'Почта РФ',
  rumisota: 'Rumisota'
}

const shippingChartColors = {
  ems: {
    current: '#fb923c',
    prev: '#fb923c'
  },
  pochta: {
    current: '#60a5fa',
    prev: '#60a5fa'
  },
  rumisota: {
    current: '#818cf8',
    prev: '#818cf8'
  }
}

let options = ref(),
  series = ref(),
  searchJournalValue = ref(''),
  ordersCounters = ref({}),
  salesCounters = ref({}),
  shippingCounters = ref({}),
  prev_shippingCounters = ref({}),
  byCategoriesCounters = ref({}),
  prev_ordersCounters = ref({}),
  prev_salesCounters = ref({}),
  canceledOrdersCounters = ref({}),
  countersLoading = ref(true),
  exLoading = ref(true),
  vcounters = ref({}),
  debtors = ref([]),
  topProducts = ref<TopProductsType>(),
  orderProccesingTimeData = ref(),
  smsbalance = ref(0),
  zadarmabalance = ref(0),
  zadarmastats = ref(),
  zadarmaclients = ref(),
  zaradmaListtLoading = ref(true),
  debtorsLoading = ref(true),
  problemOrders = ref({
    rti: [],
    rumi: []
  }),
  problemOrdersLoading = ref(true),
  mounted = ref(false)

const { isLoading, isError, data, error } = useQuery({
  queryKey: ['counters'],
  queryFn: load,
  refetchOnMount: false,
  refetchOnWindowFocus: true,
  refetchOnReconnect: true
  // retry: 3,
})

// watch(isLoading, () => //console.log('isLoading:', isLoading))

async function load() {
  const res = await Promise.all([
    await loadZadarmaStats(),
    loadProblemOrders(),
    loadCounters(),
    loadSMSru(),
    loadZadarmaBalance(),
    // loadOrderProccesingData(),
    // loadTopProducts(),
    loadDebtors(),
    // loadExCounters(),
    loadZadarmaBalance(),
    loadSMSru()
  ])

  //console.log('res:', res)

  return res
}

const trnProblemOrdersKeys = (key: string) => {
  const keys: any = {
    'paid': 'Оплачен, не отправлен более 3 дней',
    'ignored': 'Не обработан более 6 дней',
    'paused': 'В ожидании оплаты более 2 дней',
    'reserved': 'В резерве более 30 дней'
  }

  return keys[key]
}

const loadCounters = async () => {
  const res = await api('/cpan/statistics/dashboard/')
  const { ordersCount, totalSalesCount, prev_ordersCount, prev_totalSalesCount, byCategories, counters, shippingStats, prev_shippingStats } = res.body

  ordersCounters.value = ordersCount
  salesCounters.value = totalSalesCount
  shippingCounters.value = shippingStats

  prev_shippingCounters.value = prev_shippingStats
  prev_ordersCounters.value = prev_ordersCount
  prev_salesCounters.value = prev_totalSalesCount

  byCategoriesCounters.value = byCategories || {}

  countersLoading.value = false
  vcounters.value = counters

  return res.body
}

const loadProblemOrders = async () => {
  const resRti = await api('/service/checkorders?isRumi=true')
  const resRumi = await api('/service/checkorders')

  problemOrders.value.rti = resRti.body
  problemOrders.value.rumi = resRumi.body

  problemOrdersLoading.value = false

  return { resRti, resRumi }
}

const loadExCounters = async () => {
  const res = await api('/cpan/statistics/dashboard/ex')
  const { byCategories, canceledOrdersCount } = res.body

  canceledOrdersCounters.value = canceledOrdersCount
  byCategoriesCounters.value = byCategories
  exLoading.value = false

  return res?.body
}

const loadTopProducts = async () => {
  const res = await api('/cpan/statistics/topproducts')

  if (res.body) {
    topProducts.value = res.body
  }

  return res?.body
}

const loadOrderProccesingData = async () => {
  const res = await api('/cpan/statistics/orderprocessing')

  if (res.body) {
    orderProccesingTimeData.value = res.body
  }

  return res?.body
}

const searchJournal = async () => {
  const res = await api('/service/journal/search/' + encodeURIComponent(searchJournalValue.value))

  if (res.ok) {
    store.commit('fillJournal', res.body)
  }

  return res?.body
}

const loadDebtors = async () => {
  const res = await api('/cpan/clients/debtors')

  if (res.ok) {
    debtors.value = res.body
    debtorsLoading.value = false
  }

  return res?.body
}

const loadSMSru = async () => {
  const { body } = await api('/service/sms/balance')

  smsbalance.value = body.balance

  return body
}

const loadZadarmaBalance = async () => {
  const { body } = await api('/service/zadarma', {
    method: 'POST',
    data: {
      method: '/v1/info/balance/'
      // payload: {}
    }
  })

  zadarmabalance.value = body.balance

  return body
}

const loadZadarmaStats = async () => {
  const { body } = await api('/service/zadarma', {
    method: 'POST',
    data: {
      method: '/v1/statistics/',
      payload: {
        // skip: 1,
        end: dayjs().add(1, 'hour').format('YYYY-MM-DD hh:mm:ss'),
        start: dayjs().subtract(2, 'day').format('YYYY-MM-DD hh:mm:ss'),
        limit: 200
      }
    }
  })

  zadarmastats.value = body

  const { body: clients } = await api('/service/clients/byphones', {
    method: 'POST',
    data: {
      phones: [
        ...new Set(
          body.stats.map((i) => i.to),
          body.stats.map((i) => i.from)
        )
      ]
    }
  })

  zadarmastats.value.stats = zadarmastats.value.stats?.map((item) => {
    let fnd = clients.find((x) => x.client_phone.replace(/\D/gm, '') == item.to || x.client_phone.replace(/\D/gm, '') == item.from)
    return fnd ? { ...item, ...fnd } : item
  })

  zadarmastats.value.stats.reverse()

  zaradmaListtLoading.value = false

  return body
}

watch(searchJournalValue, () => {
  if (searchJournalValue.value.length > 2 || searchJournalValue.value.length == 0) {
    searchJournal()
  }
})
</script>

<template lang="">
  <div>
    <div class="mb-7">
      <CountersBlock
        v-if="!isLoading"
        :zadarmabalance="zadarmabalance"
        :smsrubalance="smsbalance"
        :ordersToday="vcounters.orders_today"
        :val="vcounters.orders_sum_today"
        :newClients="vcounters.new_clients"
        :unpaidOrders="vcounters.unpaid_orders"
      />
      <div class="flex mx-auto my-10" v-else>
        <ProgressSpinner />
      </div>
    </div>

    <div class="m-2 col-span-2 w-full grid grid-cols-7 gap-8">
      <div class="col-span-4 bcard overflow-x-auto p-5">
        <div class="text-gray-600 dark:text-gray-200 dark:text-gray-200 ttext-black font-semibold">Последние звонки</div>
        <ScrollPanel v-if="!zaradmaListtLoading" style="width: 100%; height: 250px" class="custom">
          <ZadarmaCallList class="mt-3" :data="zadarmastats" />
        </ScrollPanel>
        <div class="flex mx-auto my-10" v-else>
          <ProgressSpinner />
        </div>
      </div>

      <!-- <div class="flex col-span-3 bcard overflow-x-auto p-5">
        <TopProducts v-if="topProducts?.topSales" :topProducts="topProducts" />
        <div class="flex mx-auto" v-else>
          <ProgressSpinner />
        </div>
      </div> -->
    </div>

    <div class="flex justify-between mt-10 gap-10">
      <div class="bcard p-5 w-full">
        <span class="text-slate-600 dark:text-zinc-100 font-semibold">Должники</span>

        <ScrollPanel v-if="!debtorsLoading" style="width: 100%; height: 250px" class="custom mt-5">
          <div class="mt-3 text-slate-500 dark:text-gray-300">
            <div class="mt-2" v-for="(debtor, index) in debtors" :key="index">
              <router-link class="font-semibold" :to="'/clients/' + debtor.order_client">
                {{ index + 1 }}. {{ debtor.client_name }} <span class="text-sm text-slate-600 dark:text-zinc-100">({{ debtor.client_mail }})</span>
                -
                <span class="font-semibold text-slate-600 dark:text-zinc-100">{{ Number(debtor?.sum).toLocaleString() || '--' }} руб.</span>
              </router-link>
            </div>
          </div>
        </ScrollPanel>
        <div v-else>
          <div class="flex mx-auto my-20">
            <ProgressSpinner />
          </div>
        </div>
      </div>
      <div class="p-5 bcard w-full">
        <span class="text-slate-600 dark:text-zinc-100 font-semibold">Заказы требующие внимания</span>
        <div class="mt-3">
          <div v-if="!problemOrdersLoading" class="flex justify-center gap-5 items-start">
            <div>
              <div class="text-lg font-bold">РТИ</div>
              <ScrollPanel style="width: 100%; height: 250px" class="custom mt-5">
                <div v-for="(orders, key) in problemOrders.rti" :key="key">
                  <div>
                    <div class="font-semibold text-slate-500 dark:text-gray-300">{{ trnProblemOrdersKeys(key) }}:</div>
                  </div>
                  <div class="mm-3">
                    <div class="flex items-center space-x-2" v-for="(order, key) in orders" :key="key">
                      <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                      <router-link class="hover:underline" :to="'/orders/' + order.order_id">{{ order.order_id }}</router-link>
                    </div>
                    <hr class="my-2" />
                  </div>
                </div>
              </ScrollPanel>
            </div>

            <div class="mb-3">
              <div class="text-lg font-bold">Rumisota</div>
              <ScrollPanel style="width: 100%; height: 250px" class="custom mt-5">
                <div v-for="(orders, key) in problemOrders.rumi" :key="key">
                  <div>
                    <div class="font-semibold text-slate-500 dark:text-gray-300">{{ trnProblemOrdersKeys(key) }}:</div>
                  </div>
                  <div class="mm-3">
                    <div class="flex items-center space-x-2" v-for="(order, key) in orders" :key="key">
                      <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                      <router-link class="hover:underline" :to="'/orders/' + order.order_id">{{ order.order_id }}</router-link>
                    </div>
                    <hr class="my-2" />
                  </div>
                </div>
              </ScrollPanel>
            </div>
          </div>

          <div class="flex mx-auto my-20" v-else>
            <ProgressSpinner />
          </div>
        </div>
      </div>
    </div>

    <div v-if="false" vv-if="!countersLoading" class="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-6 mt-6">
      <div class="col-span-2 m-2 w-full grid grid-cols-8 gap-8">
        <div class="col-span-8 space-y-7">
          <div>
            <TotalSalesChart title="Чистые продажи" class="ccol-span-4 bcard p-5" v-if="salesCounters" :salesCounters="salesCounters" :prev_salesCounters="prev_salesCounters" />
            <div class="flex mx-auto" v-else>
              <ProgressSpinner />
            </div>
          </div>

          <!-- <div v-for="(item, index) in Object.keys(shippingEnums)" :key="item">
            <TotalSalesChart
              :title="'Доставка: ' + shippingEnums[item]"
              :borderColorCurrent="shippingChartColors[item].current"
              :borderColorPrevious="shippingChartColors[item].prev"
              class="bcard p-5"
              :salesCounters="shippingCounters[item]"
              :prev_salesCounters="prev_shippingCounters[item]"
            />
          </div> -->
        </div>
        <!-- <div class="col-span-3 bcard p-5">
          <div>
            <div class="flex justify-between">
              <span class="text-gray-500 dark:text-gray-200 font-semibold">Журнал</span>

              <span class="p-input-icon-right mr-3">
                <i class="pi pi-search" />
                <InputText class="border-0 bg-gray-100 h-10" type="text" v-model="searchJournalValue" placeholder="Поиск" />
              </span>
            </div>
            <ScrollPanel style="width: 100%; height: 250px" class="custom">
              <div vv-if="store.getters.getUser?.user_role == 'su'" class="mt-3">
                <div class="hover:bg-gray-100 p-2 rounded-md flex justify-start items-center space-x-3 mt-3" v-for="(item, index) in store.getters.getJournal">
                  <div class="p-2 rounded-md shadow bg-yellow-100">
                    {{ item.user_name }}
                  </div>
                  <div class="text-gray-600 dark:text-gray-200">
                    {{ item.created_at }}
                  </div>
                  <div>
                    {{ item.msg }}
                    <a :href="`/${item.entity}/${item.entity_id}`" target="_blank" class="font-bold text-gray-500 dark:text-gray-200">
                      <span class="bg-gray-200 p-2 rounded-md shadow">{{ item.entity_id }}</span>
                    </a>
                  </div>
                </div>
              </div>
            </ScrollPanel>
          </div>
        </div> -->
      </div>
      <!-- <div class="shadow p-2 rounded-xl bg-white dark:bg-zinc-900 m-2">
            <Journal/>
            <OrdersChart :ordersCounters="ordersCounters"  :prev_ordersCounters="prev_ordersCounters" />
        </div>
        
        <div class="shadow p-2 rounded-xl bg-white dark:bg-zinc-900 m-2">
            <CanceledCompare :ordersCounters="ordersCounters" :canceledOrdersCounters="canceledOrdersCounters" />
        </div> -->
    </div>
    <!-- <div class="grid grid-cols-1 lg:grid-cols-2" v-else>
      <div class="flex mx-auto">
        <ProgressSpinner />
      </div>
    </div> -->

    <!-- <div v-if="!isLoading" class="grid grid-cols-1 lg:grid-cols-9 mb-7 gap-3 sm:gap-6 mt-6">
      <div class="col-span-6 bcard p-5 m-2">
        <OrdersChart class="ccol-span-3" :ordersCounters="ordersCounters" :prev_ordersCounters="prev_ordersCounters" />
      </div>
      <div class="col-span-3 bcard p-5 m-2">
        <OrderProccesingTime v-if="orderProccesingTimeData" :data="orderProccesingTimeData" />
        <div class="flex mx-auto my-20" v-else>
          <ProgressSpinner />
        </div>
      </div>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-3" v-else>
      <div class="flex mx-auto my-20">
        <ProgressSpinner />
      </div>
    </div> -->
    <!-- <div class="flex w-auto">
      <div class="shadow p-2 px-5 rounded-xl bg-white dark:bg-zinc-900 m-2">
        <CategoriesChart :byCategoriesCounters="byCategoriesCounters" />
        <Skeleton v-if="!byCategoriesCounters" height="280px" />
      </div>
    </div> -->
  </div>
</template>

<style>
.custom .p-scrollpanel-wrapper {
  border-right: 9px solid #f4f4f4;
}

.custom .p-scrollpanel-bar {
  background-color: #5fb0ff;
  opacity: 1;
  transition: background-color 0.3s;
}

.custom .p-scrollpanel-bar:hover {
  background-color: #5fb0ff;
}
</style>
