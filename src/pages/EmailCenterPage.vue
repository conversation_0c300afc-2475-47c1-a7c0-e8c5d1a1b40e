<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useToast } from 'primevue/usetoast'
import InputSwitch from 'primevue/inputswitch'
import InputNumber from 'primevue/inputnumber'
import Editor from 'primevue/editor'
import But<PERSON> from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Tag from 'primevue/tag'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Toast from 'primevue/toast'
import ConfirmDialog from 'primevue/confirmdialog'
import RadioButton from 'primevue/radiobutton'
import ToggleButton from 'primevue/togglebutton'
import Card from 'primevue/card'
import Divider from 'primevue/divider'
import { useConfirm } from 'primevue/useconfirm'
import { FilterMatchMode } from 'primevue/api'
import { emailService, type EmailSenderData, type Recipient, type AddEmailParams } from '@/lib/emailService'
import { trpc } from '@/tRPC'
import { useQuery } from '@tanstack/vue-query'

const toast = useToast()
const confirm = useConfirm()

const { isLoading, isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryFn: async () => {
    const data = await trpc.services.getEmailSenderData.query()
    return data
  },
  queryKey: ['getEmailSenderData'],
  refetchOnMount: false,
  refetchOnWindowFocus: false
})

const senderConfig = ref<EmailSenderData>({
  enabled: false,
  scheduleType: 'interval',
  scheduleTime: 60,
  weeklySchedule: [],
  subject: '',
  message: '',
  recipients: [],
  lastSendDate: ''
})

const weekDays = [
  { label: 'Воскресенье', value: 0 },
  { label: 'Понедельник', value: 1 },
  { label: 'Вторник', value: 2 },
  { label: 'Среда', value: 3 },
  { label: 'Четверг', value: 4 },
  { label: 'Пятница', value: 5 },
  { label: 'Суббота', value: 6 }
]

const toggleWeekDay = (dayValue: number) => {
  if (!senderConfig.value.weeklySchedule) {
    senderConfig.value.weeklySchedule = []
  }

  const index = senderConfig.value.weeklySchedule.indexOf(dayValue)
  if (index > -1) {
    senderConfig.value.weeklySchedule.splice(index, 1)
  } else {
    senderConfig.value.weeklySchedule.push(dayValue)
  }
}

const isWeekDaySelected = (dayValue: number) => {
  return senderConfig.value.weeklySchedule?.includes(dayValue) || false
}

const getWeeklyScheduleText = () => {
  if (!senderConfig.value.weeklySchedule?.length) return 'Дни не выбраны'

  const selectedDays = senderConfig.value.weeklySchedule
    .sort((a, b) => a - b)
    .map((day) => weekDays.find((d) => d.value === day)?.label)
    .join(', ')

  return `Каждые: ${selectedDays}`
}

const isSaving = ref(false)
const isSending = ref(false)
const isAddingRecipient = ref(false)
const showAddDialog = ref(false)
const isEditMode = ref(false)
const editingRecipient = ref<Recipient | null>(null)

// Расширяем интерфейс для формы получателя
const newRecipient = ref<{
  email: string
  note: string
  isActive: boolean
}>({
  email: '',
  note: '',
  isActive: true
})

const filters = ref({
  global: { value: null, matchMode: FilterMatchMode.CONTAINS }
})

// Следим за изменениями данных
watch(
  data,
  (newData) => {
    if (newData) {
      senderConfig.value = {
        enabled: newData.enabled,
        scheduleType: newData.scheduleType || 'interval',
        scheduleTime: newData.scheduleTime || 60,
        weeklySchedule: newData.weeklySchedule || [],
        subject: newData.subject || '',
        message: newData.message || '',
        recipients: [...(newData.recipients || [])],
        lastSendDate: newData.lastSendDate || ''
      }
    }
  },
  { immediate: true, deep: true }
)

// Вспомогательные функции для форматирования времени
const getTimeLabels = (minutes: number) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  const hourLabel = getNumericLabel(hours, ['час', 'часа', 'часов'])
  const minLabel = getNumericLabel(mins, ['минута', 'минуты', 'минут'])
  return { hours, mins, hourLabel, minLabel }
}

const getNumericLabel = (num: number, [one, few, many]: string[]): string => {
  const absNum = Math.abs(num)
  const lastTwoDigits = absNum % 100
  const lastDigit = absNum % 10

  if (lastTwoDigits >= 11 && lastTwoDigits <= 19) return many
  if (lastDigit === 1) return one
  if (lastDigit >= 2 && lastDigit <= 4) return few
  return many
}

const saveSenderData = async () => {
  try {
    isSaving.value = true

    // Валидация в зависимости от типа расписания
    if (senderConfig.value.scheduleType === 'interval') {
      if (!senderConfig.value.scheduleTime || senderConfig.value.scheduleTime < 1) {
        senderConfig.value.scheduleTime = 60
      }
    } else if (senderConfig.value.scheduleType === 'weekly') {
      if (!senderConfig.value.weeklySchedule?.length) {
        toast.add({
          severity: 'warn',
          summary: 'Внимание',
          detail: 'Выберите хотя бы один день недели',
          life: 3000
        })
        return
      }
    }

    await trpc.services.updateEmailSenderData.mutate(senderConfig.value)

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Настройки рассылки сохранены',
      life: 3000
    })

    await refetch()
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить настройки',
      life: 5000
    })
    console.error(error)
  } finally {
    isSaving.value = false
  }
}

const sendEmailsNow = async () => {
  confirm.require({
    message: 'Вы уверены, что хотите отправить рассылку прямо сейчас?',
    header: 'Подтверждение отправки',
    icon: 'pi pi-exclamation-triangle',
    acceptLabel: 'Да, отправить',
    rejectLabel: 'Отмена',
    accept: async () => {
      try {
        isSending.value = true
        await emailService.sendEmails()
        toast.add({
          severity: 'success',
          summary: 'Успешно',
          detail: 'Рассылка запущена',
          life: 3000
        })
      } catch (error) {
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: 'Не удалось отправить письма',
          life: 5000
        })
        console.error(error)
      } finally {
        isSending.value = false
      }
    }
  })
}

const old_saveNewRecipient = async () => {
  if (!newRecipient.value.email) {
    toast.add({
      severity: 'warn',
      summary: 'Внимание',
      detail: 'Введите email',
      life: 3000
    })
    return
  }

  try {
    isAddingRecipient.value = true

    if (isEditMode.value && editingRecipient.value) {
      // Редактирование
      const index = senderConfig.value.recipients.findIndex((r) => r.email === editingRecipient.value!.email)
      if (index !== -1) {
        senderConfig.value.recipients[index] = {
          ...senderConfig.value.recipients[index],
          email: newRecipient.value.email,
          note: newRecipient.value.note,
          isActive: newRecipient.value.isActive
        }
      }
    } else {
      // Добавление нового
      const newRecipientData: Recipient = {
        email: newRecipient.value.email,
        note: newRecipient.value.note,
        isActive: newRecipient.value.isActive,
        createdAt: new Date().toISOString()
      }

      senderConfig.value.recipients = [...senderConfig.value.recipients, newRecipientData]
    }

    // Сохраняем изменения
    await trpc.services.updateEmailSenderData.mutate(senderConfig.value)

    showAddDialog.value = false
    resetRecipientForm()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: isEditMode.value ? 'Получатель обновлен' : 'Получатель добавлен',
      life: 3000
    })

    await refetch()
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить получателя',
      life: 5000
    })
    console.error(error)
  } finally {
    isAddingRecipient.value = false
  }
}

const saveNewRecipient = async () => {
  if (!newRecipient.value.email) {
    toast.add({
      severity: 'warn',
      summary: 'Внимание',
      detail: 'Введите email',
      life: 3000
    })
    return
  }

  // Проверка на дублирование email
  const emailExists = senderConfig.value.recipients.some((recipient) => {
    // При редактировании исключаем текущий email из проверки
    if (isEditMode.value && editingRecipient.value) {
      return recipient.email.toLowerCase() === newRecipient.value.email.toLowerCase() && recipient.email !== editingRecipient.value.email
    }
    // При добавлении нового проверяем все существующие
    return recipient.email.toLowerCase() === newRecipient.value.email.toLowerCase()
  })

  if (emailExists) {
    toast.add({
      severity: 'warn',
      summary: 'Внимание',
      detail: 'Получатель с таким email уже существует',
      life: 3000
    })
    return
  }

  try {
    isAddingRecipient.value = true

    if (isEditMode.value && editingRecipient.value) {
      // Редактирование
      const index = senderConfig.value.recipients.findIndex((r) => r.email === editingRecipient.value!.email)
      if (index !== -1) {
        senderConfig.value.recipients[index] = {
          ...senderConfig.value.recipients[index],
          email: newRecipient.value.email,
          note: newRecipient.value.note,
          isActive: newRecipient.value.isActive
        }
      }
    } else {
      // Добавление нового
      const newRecipientData: Recipient = {
        email: newRecipient.value.email,
        note: newRecipient.value.note,
        isActive: newRecipient.value.isActive,
        createdAt: new Date().toISOString()
      }

      senderConfig.value.recipients = [...senderConfig.value.recipients, newRecipientData]
    }

    // Сохраняем изменения
    await trpc.services.updateEmailSenderData.mutate(senderConfig.value)

    showAddDialog.value = false
    resetRecipientForm()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: isEditMode.value ? 'Получатель обновлен' : 'Получатель добавлен',
      life: 3000
    })

    await refetch()
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить получателя',
      life: 5000
    })
    console.error(error)
  } finally {
    isAddingRecipient.value = false
  }
}

const editRecipient = (recipient: Recipient) => {
  isEditMode.value = true
  editingRecipient.value = recipient
  newRecipient.value = {
    email: recipient.email,
    note: recipient.note || '',
    isActive: recipient.isActive
  }
  showAddDialog.value = true
}

const deleteRecipient = (recipient: Recipient) => {
  confirm.require({
    message: `Вы уверены, что хотите удалить получателя ${recipient.email}?`,
    header: 'Подтверждение удаления',
    icon: 'pi pi-exclamation-triangle',
    acceptLabel: 'Да',
    rejectLabel: 'Отмена',
    accept: async () => {
      try {
        senderConfig.value.recipients = senderConfig.value.recipients.filter((r) => r.email !== recipient.email)

        await trpc.services.updateEmailSenderData.mutate(senderConfig.value)

        toast.add({
          severity: 'success',
          summary: 'Успешно',
          detail: 'Получатель удален',
          life: 3000
        })

        await refetch()
      } catch (error) {
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: 'Не удалось удалить получателя',
          life: 5000
        })
        console.error(error)
      }
    }
  })
}

const resetRecipientForm = () => {
  newRecipient.value = {
    email: '',
    note: '',
    isActive: true
  }
  isEditMode.value = false
  editingRecipient.value = null
}

const openAddDialog = () => {
  resetRecipientForm()
  showAddDialog.value = true
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleString('ru-RU')
}

const formatScheduleHelper = (minutes: number | null): string => {
  if (!minutes) return ''

  const { hours, mins, hourLabel, minLabel } = getTimeLabels(minutes)

  if (hours === 0) {
    return `${mins} ${minLabel}`
  }

  return mins === 0 ? `${hours} ${hourLabel}` : `${hours} ${hourLabel} ${mins} ${minLabel}`
}

const getPresetIntervals = () => [
  { label: '1 неделя', value: 10080 }, // 7 дней * 24 часа * 60 минут
  { label: '1 месяц', value: 43200 }, // 30 дней * 24 часа * 60 минут
  { label: '2 месяца', value: 86400 } // 60 дней * 24 часа * 60 минут
]
</script>

<template>
  <div class="p-4">
    <h1 class="text-3xl font-bold mb-6">Центр рассылки</h1>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Левая колонка - Настройки -->
      <div>
        <Card>
          <template #title>
            <h2 class="text-xl">Настройки рассылки</h2>
          </template>
          <template #content>
            <div class="space-y-6">
              <!-- Статус -->
              <div class="flex items-center justify-between">
                <label class="font-medium">Статус рассылки</label>
                <InputSwitch v-model="senderConfig.enabled" />
              </div>

              <!-- <div>
                <label class="block font-medium mb-3">Тип расписания</label>
                <div class="space-y-3">
                  <div class="flex items-center">
                    <RadioButton inputId="interval" name="scheduleType" value="interval" v-model="senderConfig.scheduleType" :disabled="!senderConfig.enabled" />
                    <label for="interval" class="ml-2">По интервалу</label>
                  </div>
                  <div class="flex items-center">
                    <RadioButton inputId="weekly" name="scheduleType" value="weekly" v-model="senderConfig.scheduleType" :disabled="!senderConfig.enabled" />
                    <label for="weekly" class="ml-2">По дням недели</label>
                  </div>
                </div>
              </div>

              <div v-if="senderConfig.scheduleType === 'interval'">
                <label class="block font-medium mb-3">Интервал между отправками</label>
                <div class="space-y-4">
                  <div class="flex items-center gap-3">
                    <InputNumber v-model="senderConfig.scheduleTime" :min="1" :max="86400" :disabled="!senderConfig.enabled" class="w-32" />
                    <span class="text-sm text-gray-600">
                      {{ formatScheduleHelper(senderConfig.scheduleTime) }}
                    </span>
                  </div>

                  <div class="grid grid-cols-2 gap-2">
                    <Button
                      v-for="interval in getPresetIntervals()"
                      :key="interval.value"
                      :label="interval.label"
                      size="small"
                      :outlined="senderConfig.scheduleTime !== interval.value"
                      :disabled="!senderConfig.enabled"
                      @click="senderConfig.scheduleTime = interval.value"
                    />
                  </div>
                </div>
              </div>

              <div v-if="senderConfig.scheduleType === 'weekly'">
                <label class="block font-medium mb-3">Дни недели для отправки</label>
                <div class="space-y-4">
                  <div class="grid grid-cols-2 gap-2">
                    <ToggleButton
                      v-for="day in weekDays"
                      :key="day.value"
                      :modelValue="isWeekDaySelected(day.value)"
                      @update:modelValue="toggleWeekDay(day.value)"
                      :onLabel="day.label"
                      :offLabel="day.label"
                      :disabled="!senderConfig.enabled"
                      class="text-sm"
                    />
                  </div>

                  <div class="p-3 bg-gray-50 rounded text-sm">
                    {{ getWeeklyScheduleText() }}
                  </div>
                </div>
              </div> -->

              <!-- Тема -->
              <div>
                <label class="block font-medium mb-2">Тема письма</label>
                <InputText v-model="senderConfig.subject" class="w-full" placeholder="Введите тему письма" />
              </div>

              <!-- Сообщение -->
              <div>
                <label class="block font-medium mb-2">Текст письма</label>
                <Editor v-model="senderConfig.message" editorStyle="height: 300px" />
              </div>

              <!-- Последняя отправка -->
              <div v-if="senderConfig.lastSendDate" class="p-3 bg-blue-50 rounded">
                <div class="flex items-center gap-2 text-sm">
                  <i class="pi pi-clock"></i>
                  <span>Последняя отправка: {{ formatDate(senderConfig.lastSendDate) }}</span>
                </div>
              </div>

              <!-- Кнопки -->
              <div class="flex gap-3">
                <Button @click="saveSenderData" :loading="isSaving" label="Сохранить" icon="pi pi-save" />
                <Button @click="sendEmailsNow" :loading="isSending" severity="success" label="Отправить сейчас" icon="pi pi-send" />
              </div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Правая колонка - Получатели -->
      <div>
        <Card>
          <template #title>
            <div class="flex items-center justify-between">
              <h2 class="text-xl">Получатели</h2>
              <Button size="small" @click="openAddDialog" icon="pi pi-plus" label="Добавить" />
            </div>
          </template>
          <template #content>
            <div class="space-y-4">
              <!-- Поиск -->
              <div>
                <InputText v-model="filters.global.value" placeholder="Поиск..." class="w-full" />
              </div>

              <!-- Таблица -->
              <DataTable
                v-model:filters="filters"
                :value="senderConfig.recipients"
                :loading="isLoading"
                dataKey="email"
                :rows="200"
                paginator
                class="p-datatable-sm"
                :scrollable="true"
                scrollHeight="700px"
              >
                <template #empty>
                  <div class="text-center py-8">
                    <i class="pi pi-inbox text-4xl text-gray-400 block mb-3"></i>
                    <p class="text-gray-600">Нет получателей</p>
                  </div>
                </template>

                <Column field="email" header="Email" :sortable="true">
                  <template #body="{ data }">
                    <span class="font-medium">{{ data.email }}</span>
                  </template>
                </Column>

                <Column field="note" header="Заметка">
                  <template #body="{ data }">
                    <span class="text-gray-600">{{ data.note || '-' }}</span>
                  </template>
                </Column>

                <Column field="createdAt" header="Добавлен" :sortable="true">
                  <template #body="{ data }">
                    <span class="text-sm">{{ formatDate(data.createdAt) }}</span>
                  </template>
                </Column>

                <Column field="isActive" header="Статус">
                  <template #body="{ data }">
                    <Tag :severity="data.isActive ? 'success' : 'danger'">
                      {{ data.isActive ? 'Активен' : 'Отключен' }}
                    </Tag>
                  </template>
                </Column>

                <Column style="width: 100px">
                  <template #body="{ data }">
                    <div class="flex gap-1">
                      <Button icon="pi pi-pencil" outlined rounded size="small" @click="editRecipient(data)" />
                      <Button icon="pi pi-trash" outlined rounded severity="danger" size="small" @click="deleteRecipient(data)" />
                    </div>
                  </template>
                </Column>
              </DataTable>
            </div>
          </template>
        </Card>
      </div>
    </div>

    <!-- Диалог -->
    <Dialog v-model:visible="showAddDialog" :style="{ width: '450px' }" :header="isEditMode ? 'Редактировать получателя' : 'Добавить получателя'" :modal="true">
      <div class="space-y-4">
        <div>
          <label class="block font-medium mb-2">Email</label>
          <InputText v-model="newRecipient.email" type="email" class="w-full" placeholder="<EMAIL>" />
        </div>

        <div>
          <label class="block font-medium mb-2">Заметка</label>
          <InputText v-model="newRecipient.note" class="w-full" placeholder="Дополнительная информация" />
        </div>

        <div class="flex items-center justify-between">
          <label class="font-medium">Активен</label>
          <InputSwitch v-model="newRecipient.isActive" />
        </div>
      </div>

      <template #footer>
        <Button label="Отмена" icon="pi pi-times" outlined @click="showAddDialog = false" />
        <Button label="Сохранить" icon="pi pi-check" @click="saveNewRecipient" :loading="isAddingRecipient" />
      </template>
    </Dialog>

    <Toast />
    <ConfirmDialog />
  </div>
</template>

<style scoped>
.p-column-filter {
  width: 100%;
}
</style>
