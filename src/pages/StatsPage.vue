<script setup lang="ts">
import CategoriesChart from '@/components/echarts/CategoriesChart.vue'
import ProductStatByFilter from '@/components/dashboard/ProductStatByFilter.vue'
import TabPanel from 'primevue/tabpanel'
import TabView from 'primevue/tabview'
import BrandsChart from '@/components/echarts/BrandsChart.vue'
import ShippingCharts from '@/components/echarts/ShippingCharts.vue'
import ShopCharts from '@/components/echarts/ShopCharts.vue'
import QtyCharts from '@/components/echarts/QtyCharts.vue'
import { onMounted, ref } from 'vue'
import StatList from '@/components/StatList.vue'
import StatsDownload from '@/components/StatsDownload.vue'
import ShippingTypeChart from '@/components/echarts/ShippingTypeChart.vue'

const activeTab = ref(0)

onMounted(() => {
  try {
    document.title = 'Статистика'
  } catch (error) {}
})
</script>

<template>
  <div>
    <div>
      <TabView v-model:activeIndex="activeTab">
        <TabPanel key="statlist" header="Спрос">
          <div v-if="activeTab == 0">
            <!-- <div class="flex justify-end container px-20 mb-5">
              <StatsDownload />
            </div> -->
            <StatList />
          </div>
        </TabPanel>
        <TabPanel key="all" header="Графики">
          <div v-if="activeTab == 1" class="space-y-10">
            <div>
              <div class="flex gap-10">
                <div class="w-1/2 shadow-xl shadow-slate-200 dark:shadow-none rounded-md">
                  <ShopCharts />
                </div>
                <div class="w-1/2 shadow-xl shadow-slate-200 dark:shadow-none rounded-md">
                  <ShippingCharts />
                </div>
              </div>
            </div>
            <div class="flex gap-10">
              <div class="w-1/2 shadow-xl shadow-slate-200 dark:shadow-none rounded-md">
                <QtyCharts />
              </div>
              <div class="w-1/2 shadow-xl shadow-slate-200 dark:shadow-none rounded-md">
                <ShippingTypeChart />
              </div>
            </div>
            <div class="shadow-xl shadow-slate-200 dark:shadow-none rounded-md">
              <CategoriesChart />
            </div>
            <div class="shadow-xl shadow-slate-200 dark:shadow-none rounded-md">
              <BrandsChart />
            </div>
          </div>
        </TabPanel>
        <TabPanel key="config" header="Конфигуратор">
          <div v-if="activeTab == 2">
            <ProductStatByFilter />
          </div>
        </TabPanel>
      </TabView>
    </div>
  </div>
</template>
