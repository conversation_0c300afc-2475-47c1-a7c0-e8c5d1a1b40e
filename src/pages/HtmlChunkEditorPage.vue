<script setup lang="ts">
import { onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuery, useMutation } from '@tanstack/vue-query'
import { useToast } from 'primevue/usetoast'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Message from 'primevue/message'
import Dialog from 'primevue/dialog'

import { trpc } from '@/tRPC'

import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import Image from '@tiptap/extension-image'
import { Color } from '@tiptap/extension-color'
import { TextStyle, BackgroundColor, FontSize, FontFamily } from '@tiptap/extension-text-style'
import Paragraph from '@tiptap/extension-paragraph'
import Heading from '@tiptap/extension-heading'
import BulletList from '@tiptap/extension-bullet-list'
import OrderedList from '@tiptap/extension-ordered-list'
import ListItem from '@tiptap/extension-list-item'
import CodeBlock from '@tiptap/extension-code-block'
import Blockquote from '@tiptap/extension-blockquote'
import HorizontalRule from '@tiptap/extension-horizontal-rule'
import { Table, TableRow, TableHeader, TableCell } from '@tiptap/extension-table'
import { Node } from '@tiptap/core'

type HtmlChunk = {
  id: number
  keyname: string
  title: string
  body: string
}

const route = useRoute()
const router = useRouter()
const toast = useToast()

const params = reactive<{ id?: number; key?: string }>({
  id: route.query.id ? Number(route.query.id) : undefined,
  key: (route.query.key as string) || undefined
})

const editor = ref<Editor | null>(null)
const chunk = ref<HtmlChunk | null>(null)
const isDirty = ref(false)
const showSource = ref(false)
const sourceMode = ref<'html' | 'json'>('html')
const sourceText = ref('')

const {
  isLoading,
  isError,
  data,
  refetch
} = useQuery({
  queryKey: ['htmlChunk', params.id, params.key],
  queryFn: async () => {
    if (!params.id && !params.key) {
      throw new Error('Параметр id или key обязателен')
    }
    const res = await trpc.services.getHtmkChunk.query({ id: params.id, key: params.key })
    chunk.value = res as unknown as HtmlChunk
    return chunk.value
  },
  refetchOnWindowFocus: false
})

const { mutateAsync: saveAsync, isLoading: isSaving } = useMutation({
  mutationKey: ['updateHtmlChunk'],
  mutationFn: async (payload: { id?: number; key?: string; body?: string }) =>
    trpc.services.updateHtmlChunk.mutate(payload)
})

onMounted(() => {
  const withClassAttr = <T extends any>(Ext: T & { extend: Function }) =>
    // @ts-ignore
    (Ext as any).extend({
      addAttributes() {
        // @ts-ignore
        const parent = this.parent?.()
        return {
          ...(parent || {}),
          class: {
            default: null,
            parseHTML: (element: HTMLElement) => element.getAttribute('class'),
            renderHTML: (attributes: Record<string, any>) =>
              attributes.class ? { class: attributes.class } : {}
          }
        }
      }
    })

  const Section = Node.create({
    name: 'section',
    group: 'block',
    content: 'block*',
    addAttributes() {
      return {
        class: {
          default: null,
          parseHTML: (element: HTMLElement) => element.getAttribute('class'),
          renderHTML: (attributes: Record<string, any>) =>
            attributes.class ? { class: attributes.class } : {}
        }
      }
    },
    parseHTML() {
      return [{ tag: 'section' }]
    },
    renderHTML({ HTMLAttributes }) {
      return ['section', HTMLAttributes, 0]
    }
  })

  const Div = Node.create({
    name: 'div',
    group: 'block',
    content: 'block*',
    addAttributes() {
      return {
        class: {
          default: null,
          parseHTML: (element: HTMLElement) => element.getAttribute('class'),
          renderHTML: (attributes: Record<string, any>) => (attributes.class ? { class: attributes.class } : {})
        }
      }
    },
    parseHTML() {
      return [{ tag: 'div' }]
    },
    renderHTML({ HTMLAttributes }) {
      return ['div', HTMLAttributes, 0]
    }
  })

  const LinkWithAttrs = Link.extend({
    addAttributes() {
      const parent = this.parent?.() || {}
      return {
        ...parent,
        class: {
          default: null,
          parseHTML: (el: HTMLElement) => el.getAttribute('class'),
          renderHTML: (attrs: Record<string, any>) => (attrs.class ? { class: attrs.class } : {})
        },
        target: {
          default: null,
          parseHTML: (el: HTMLElement) => el.getAttribute('target'),
          renderHTML: (attrs: Record<string, any>) => (attrs.target ? { target: attrs.target } : {})
        },
        rel: {
          default: null,
          parseHTML: (el: HTMLElement) => el.getAttribute('rel'),
          renderHTML: (attrs: Record<string, any>) => (attrs.rel ? { rel: attrs.rel } : {})
        }
      }
    }
  }).configure({ openOnClick: true, autolink: true, defaultProtocol: 'https' })

  editor.value = new Editor({
    extensions: [
      StarterKit.configure({
        heading: { levels: [1, 2, 3, 4, 5, 6] }
      }),
      Underline,
      LinkWithAttrs,
      withClassAttr(Image),
      Color,
      TextStyle,
      BackgroundColor,
      FontSize,
      FontFamily,
      withClassAttr(Paragraph),
      withClassAttr(Heading),
      withClassAttr(BulletList),
      withClassAttr(OrderedList),
      withClassAttr(ListItem),
      withClassAttr(CodeBlock),
      withClassAttr(Blockquote),
      withClassAttr(HorizontalRule),
      Table.configure({ resizable: true }),
      TableRow,
      TableHeader,
      TableCell,
      TextAlign.configure({ types: ['heading', 'paragraph'] }),
      Section,
      Div
    ],
    content: '<p></p>',
    editorProps: {
      attributes: {
        class: 'format lg:format-lg dark:format-invert focus:outline-none format-blue max-w-none min-h-[60vh]'
      }
    },
    onUpdate: () => {
      isDirty.value = true
    }
  })

  const onKeyDown = (e: KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 's') {
      e.preventDefault()
      handleSave()
    }
  }
  window.addEventListener('keydown', onKeyDown)
  // сохраняем обработчик для удаления
  ;(onKeyDown as any)._id = 'save-shortcut'
})

onBeforeUnmount(() => {
  editor.value?.destroy()
  // снять обработчик ctrl+s
  // перебор событий не требуется; удалим по ссылке, если указывали отдельно
})

watch(
  () => data?.value,
  (val) => {
    if (val && editor.value) {
      editor.value.commands.setContent(val.body || '')
      isDirty.value = false
    }
  }
)

async function handleSave() {
  if (!editor.value) return
  const html = editor.value.getHTML()
  await saveAsync({ id: params.id, key: params.key, body: html })
  isDirty.value = false
  toast.add({ severity: 'success', summary: 'Сохранено', life: 2500 })
}

// Защита от ухода со страницы с несохранёнными правками
window.addEventListener('beforeunload', (e) => {
  if (!isDirty.value) return
  e.preventDefault()
  e.returnValue = ''
})

function insertLink() {
  if (!editor.value) return
  const url = window.prompt('Введите ссылку (URL):', 'https://')
  if (!url) return
  editor.value.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
}

function unsetLink() {
  editor.value?.chain().focus().unsetLink().run()
}

function insertImage() {
  if (!editor.value) return
  const url = window.prompt('Введите URL изображения:')
  if (!url) return
  editor.value.chain().focus().setImage({ src: url }).run()
}

function toggleSource(mode: 'html' | 'json') {
  if (!editor.value) return
  sourceMode.value = mode
  if (mode === 'html') {
    // Показываем реальный сохранённый HTML-исходник, а не нормализованный TipTap
    sourceText.value = chunk.value?.body || ''
  } else {
    sourceText.value = JSON.stringify(editor.value.getJSON(), null, 2)
  }
  showSource.value = true
}

function copySource() {
  navigator.clipboard.writeText(sourceText.value)
}

function applySourceToEditor() {
  if (!editor.value) return
  editor.value.commands.setContent(sourceText.value || '')
}

async function saveSource() {
  const html = sourceText.value || ''
  await saveAsync({ id: params.id, key: params.key, body: html })
  if (chunk.value) chunk.value.body = html
  toast.add({ severity: 'success', summary: 'Исходник сохранён', life: 2500 })
  isDirty.value = false
}

function setTextColor(e: Event) {
  const value = (e.target as HTMLInputElement).value
  editor.value?.chain().focus().setColor(value).run()
}

function unsetTextColor() {
  editor.value?.chain().focus().unsetColor().run()
}

function setBackground(e: Event) {
  const value = (e.target as HTMLInputElement).value
  editor.value?.chain().focus().setBackgroundColor(value).run()
}

function unsetBackground() {
  editor.value?.chain().focus().unsetBackgroundColor().run()
}

function setFontSizePx(size: string) {
  if (!size) {
    editor.value?.chain().focus().unsetFontSize().run()
  } else {
    editor.value?.chain().focus().setFontSize(size).run()
  }
}

function setFontFamilyName(ff: string) {
  if (!ff) {
    editor.value?.chain().focus().unsetFontFamily().run()
  } else {
    editor.value?.chain().focus().setFontFamily(ff).run()
  }
}

function clearFormatting() {
  editor.value?.chain().focus().unsetAllMarks().clearNodes().run()
}
</script>

<template>
  <div class="flex flex-col space-y-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <h2 class="text-lg font-semibold">Редактор HTML-чанка</h2>
        <Message v-if="isError" severity="error" :closable="false">Ошибка загрузки</Message>
        <Message v-else-if="isLoading" severity="info" :closable="false">Загрузка…</Message>
        <Message v-else-if="isDirty" severity="warn" :closable="false">Есть несохранённые изменения</Message>
      </div>
      <div class="flex items-center space-x-2">
        <Button :loading="isSaving" @click="handleSave" severity="success" label="Сохранить" icon="pi pi-save" />
        <Button @click="router.back()" label="Назад" icon="pi pi-chevron-left" text />
      </div>
    </div>

    <div v-if="chunk" class="flex items-center space-x-4">
      <span class="text-sm text-gray-500 dark:text-zinc-300">ID: {{ chunk.id }}</span>
      <span class="text-sm text-gray-500 dark:text-zinc-300">Key: <code>{{ chunk.keyname }}</code></span>
      <span class="text-sm text-gray-500 dark:text-zinc-300">Title:</span>
      <InputText v-model="chunk.title" disabled class="opacity-70" />
    </div>

    <div class="w-full border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
      <div class="px-3 py-2 border-b border-gray-200 dark:border-gray-600">
        <div class="flex flex-wrap items-center">
          <div class="flex items-center space-x-1 rtl:space-x-reverse flex-wrap">
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().toggleBold().run()" :class="{ 'text-blue-600': editor?.isActive('bold') }">
              <i class="pi pi-bold text-base"></i>
              <span class="sr-only">Bold</span>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().toggleItalic().run()" :class="{ 'text-blue-600': editor?.isActive('italic') }">
              <i class="pi pi-italic text-base"></i>
              <span class="sr-only">Italic</span>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().toggleUnderline().run()" :class="{ 'text-blue-600': editor?.isActive('underline') }">
              <i class="pi pi-underline text-base"></i>
              <span class="sr-only">Underline</span>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().toggleStrike().run()" :class="{ 'text-blue-600': editor?.isActive('strike') }">
              <span class="text-xs line-through">S</span>
              <span class="sr-only">Strike</span>
            </button>

            <span class="mx-2 h-5 w-px bg-gray-200 dark:bg-gray-500"></span>

            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().toggleBulletList().run()" :class="{ 'text-blue-600': editor?.isActive('bulletList') }">
              <i class="pi pi-list text-base"></i>
              <span class="sr-only">UL</span>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().toggleOrderedList().run()" :class="{ 'text-blue-600': editor?.isActive('orderedList') }">
              <i class="pi pi-sort-numeric-down text-base"></i>
              <span class="sr-only">OL</span>
            </button>

            <span class="mx-2 h-5 w-px bg-gray-200 dark:bg-gray-500"></span>

            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().toggleBlockquote().run()" :class="{ 'text-blue-600': editor?.isActive('blockquote') }">
              <i class="pi pi-quote-left text-base"></i>
              <span class="sr-only">Quote</span>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().toggleCodeBlock().run()" :class="{ 'text-blue-600': editor?.isActive('codeBlock') }">
              <i class="pi pi-code text-base"></i>
              <span class="sr-only">Code</span>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().setHorizontalRule().run()">
              <i class="pi pi-minus text-base"></i>
              <span class="sr-only">HR</span>
            </button>

            <span class="mx-2 h-5 w-px bg-gray-200 dark:bg-gray-500"></span>

            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().setParagraph().run()" :class="{ 'text-blue-600': editor?.isActive('paragraph') }">
              <span class="text-xs">P</span>
            </button>
            <button v-for="lvl in [1,2,3]" :key="lvl" type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().toggleHeading({ level: lvl }).run()" :class="{ 'text-blue-600': editor?.isActive('heading', { level: lvl }) }">
              <span class="text-xs">H{{ lvl }}</span>
            </button>

            <span class="mx-2 h-5 w-px bg-gray-200 dark:bg-gray-500"></span>

            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().setTextAlign('left').run()" :class="{ 'text-blue-600': editor?.isActive({ textAlign: 'left' }) }">
              <i class="pi pi-align-left text-base"></i>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().setTextAlign('center').run()" :class="{ 'text-blue-600': editor?.isActive({ textAlign: 'center' }) }">
              <i class="pi pi-align-center text-base"></i>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().setTextAlign('right').run()" :class="{ 'text-blue-600': editor?.isActive({ textAlign: 'right' }) }">
              <i class="pi pi-align-right text-base"></i>
            </button>

            <span class="mx-2 h-5 w-px bg-gray-200 dark:bg-gray-500"></span>

            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="insertLink">
              <i class="pi pi-link text-base"></i>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="unsetLink">
              <i class="pi pi-times text-base"></i>
            </button>

            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="insertImage">
              <i class="pi pi-image text-base"></i>
            </button>

            <span class="mx-2 h-5 w-px bg-gray-200 dark:bg-gray-500"></span>

            <!-- Цвет текста -->
            <label class="flex items-center space-x-2 text-xs text-gray-600 dark:text-gray-300 px-2">
              <span>Цвет</span>
              <input type="color" class="h-6 w-6 p-0 border-0 bg-transparent" @input="setTextColor" />
              <button type="button" class="text-xs underline" @click="unsetTextColor">сброс</button>
            </label>

            <!-- Цвет фона текста -->
            <label class="flex items-center space-x-2 text-xs text-gray-600 dark:text-gray-300 px-2">
              <span>Фон</span>
              <input type="color" class="h-6 w-6 p-0 border-0 bg-transparent" @input="setBackground" />
              <button type="button" class="text-xs underline" @click="unsetBackground">сброс</button>
            </label>

            <!-- Размер шрифта -->
            <label class="flex items-center space-x-2 text-xs text-gray-600 dark:text-gray-300 px-2">
              <span>Размер</span>
              <select class="bg-transparent border border-gray-300 dark:border-gray-600 rounded px-1 py-0.5 text-xs" @change="(e:any) => setFontSizePx(e.target.value)">
                <option value="">По умолчанию</option>
                <option value="12px">12</option>
                <option value="14px">14</option>
                <option value="16px">16</option>
                <option value="18px">18</option>
                <option value="24px">24</option>
                <option value="36px">36</option>
              </select>
            </label>

            <!-- Семейство шрифта -->
            <label class="flex items-center space-x-2 text-xs text-gray-600 dark:text-gray-300 px-2">
              <span>Шрифт</span>
              <select class="bg-transparent border border-gray-300 dark:border-gray-600 rounded px-1 py-0.5 text-xs" @change="(e:any) => setFontFamilyName(e.target.value)">
                <option value="">По умолчанию</option>
                <option value="Arial">Arial</option>
                <option value="Courier New">Courier New</option>
                <option value="Georgia">Georgia</option>
                <option value="Tahoma">Tahoma</option>
                <option value="Times New Roman">Times New Roman</option>
                <option value="Trebuchet MS">Trebuchet MS</option>
                <option value="Verdana">Verdana</option>
              </select>
            </label>

            <span class="mx-2 h-5 w-px bg-gray-200 dark:bg-gray-500"></span>

            <!-- Исходный код -->
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="toggleSource('html')">
              <i class="pi pi-code text-base"></i>
              <span class="sr-only">HTML</span>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="toggleSource('json')">
              <i class="pi pi-list text-base"></i>
              <span class="sr-only">JSON</span>
            </button>

            <span class="mx-2 h-5 w-px bg-gray-200 dark:bg-gray-500"></span>

            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="clearFormatting">
              <i class="pi pi-times text-base"></i>
              <span class="sr-only">Очистить формат</span>
            </button>

            <span class="mx-2 h-5 w-px bg-gray-200 dark:bg-gray-500"></span>

            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().undo().run()">
              <i class="pi pi-undo text-base"></i>
            </button>
            <button type="button" class="p-1.5 text-gray-500 rounded-sm hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-600" @click="editor?.chain().focus().redo().run()">
              <i class="pi pi-redo text-base"></i>
            </button>
          </div>
        </div>
      </div>
      <div class="px-3 py-2 bg-white dark:bg-zinc-800">
        <EditorContent :editor="editor" />
      </div>
    </div>
    <Dialog v-model:visible="showSource" modal dismissableMask :header="sourceMode === 'html' ? 'HTML' : 'JSON'" :style="{ width: '60rem' }" :breakpoints="{ '1199px': '75vw', '575px': '95vw' }">
      <div class="space-y-3">
        <div class="flex items-center gap-2 text-sm">
          <Button label="HTML" size="small" :severity="sourceMode==='html' ? 'primary' : undefined" text @click="toggleSource('html')" />
          <Button label="JSON" size="small" :severity="sourceMode==='json' ? 'primary' : undefined" text @click="toggleSource('json')" />
        </div>
        <textarea class="w-full h-[60vh] bg-zinc-900 text-green-300 p-3 rounded resize-none font-mono text-xs" v-model="sourceText"></textarea>
        <div class="flex justify-end space-x-2">
          <Button label="В редактор" icon="pi pi-arrow-left" @click="applySourceToEditor" />
          <Button label="Сохранить" icon="pi pi-save" severity="success" @click="saveSource" />
          <Button label="Копировать" icon="pi pi-copy" @click="copySource" />
        </div>
      </div>
    </Dialog>
  </div>
</template>

<style scoped>
</style>


